# Spring AI SDK Documentation

## Table of Contents
- [Overview](#overview)
- [Getting Started](#getting-started)
  - [Dependencies](#dependencies)
  - [Basic Configuration](#basic-configuration)
- [Configuration Details](#configuration-details)
  - [Mandatory Properties](#mandatory-properties)
  - [Optional Properties](#optional-properties)
  - [Vector Store Configuration](#vector-store-configuration)
  - [Redis Configuration](#redis-configuration)
- [Limits and Constraints](#limits-and-constraints)

## Overview

The Spring AI SDK provides integration capabilities for AI features in Spring applications, including vector store operations, chat functionality, and audit logging.

## Getting Started

### Dependencies

Add the following dependency to your `pom.xml`:

```xml
<dependency>
    <groupId>com.enttribe.commons</groupId>
    <artifactId>ai</artifactId>
    <version>2.1.5</version>
</dependency>
```

### Basic Configuration

1. Add the following package to your ComponentScan:
```java
@ComponentScan(basePackages = {"com.enttribe.commons.ai"})
```

## Configuration Details

### Mandatory Properties

Add the following required property in your `application.properties`:

```properties
commons.ai.sdk.app.name=YOUR_APP_NAME
```

### Optional Properties

#### 1. Development Mode
```properties
commons.ai.sdk.is_local=false
```
- Default: `false`
- Set to `true` only for local development and testing
- Must be `false` in production environments

#### 2. Prompt Management
```properties
commons.ai.sdk.prompt.refresh.interval=900000
```
- Default: 900000 milliseconds (15 minutes)
- Controls how often prompts are refreshed
- Example for 5-minute refresh: `300000`

#### 3. Audit Configuration
```properties
prompt.audit.enable=false
exception.audit.enable=false
```
- When enabled, logs interactions to respective audit tables:
  - `PROMPT_AUDIT`: LLM interactions
  - `EXCEPTION_AUDIT`: Exception tracking

### Vector Store Configuration

Configure vector store using the `commons.ai.sdk.vector_store.config` property:

```json
[
  {
    "vector_store_id": "<unique_identifier>",
    "vectorDatabase": "milvus",
    "inference": "groq",
    "embeddingModel": "nomic-embed-text-v1_5-preview1",
    "host": "localhost",
    "port": 19530,
    "username": "",
    "password": "",
    "databaseName": "default",
    "collectionName": "vector_store_hrms",
    "embeddingDimension": 768,
    "indexType": "IVF_FLAT",
    "metricType": "COSINE",
    "initializeSchema": true
  }
]
```

#### Vector Store Configuration Fields

| Field | Description | Required |
|-------|-------------|----------|
| vector_store_id | Unique identifier for the vector store | Yes |
| vectorDatabase | Type of vector database (currently only supports milvus) | Yes |
| inference | Inference engine for document Q&A | Yes |
| embeddingModel | Model used for generating embeddings | Yes |
| host | Vector database hostname | Yes |
| port | Vector database port | Yes |
| collectionName | Name of the collection for document storage | Yes |
| username | Database username | No |
| password | Database password | No |
| databaseName | Name of the database | No |
| embeddingDimension | Dimension of the embedding vectors | No |
| indexType | Type of index for vector search | No |
| metricType | Distance metric for similarity search | No |
| initializeSchema | Whether to initialize the database schema | No |

> **Note**: Vector store configuration will be simplified in future releases to require only collection names and optional properties.

### Redis Configuration

Redis provides persistent storage for chat messages in distributed environments. Enable Redis by adding these properties to your `application.properties`:

```properties
redis.enable=true
redis.sentinel.master=mymaster
redis.sentinel.nodes=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381
redis.password=<password>
```

If Redis properties are not configured, the SDK defaults to in-memory storage.

#### Message Storage Limits
```properties
# Maximum number of stored messages
commons.ai.sdk.chat-size=15
```
- Default: 15 messages
- Applies to both Redis and in-memory storage
- Controls the number of messages retained in the conversation history
- Can be adjusted based on your application's memory constraints and requirements

## Limits and Constraints

The SDK enforces certain limits to ensure optimal performance and resource utilization. These limits can be customized through properties in your `application.properties` file.

### Vector Store Limits

```properties
# Maximum tokens per document
commons.ai.sdk.vector.max.token.per_document=50000

# Maximum documents per request
commons.ai.sdk.vector.max.document.per_request=50
```

#### Token Limit
- Default: 50,000 tokens
- Controls the maximum number of tokens that can be processed in a single document
- Exceeding this limit will result in an exception
- Can be adjusted based on your specific needs and model capabilities

#### Document Limit
- Default: 50 documents
- Defines the maximum number of documents that can be processed in a single request
- Helps prevent memory overload and ensures stable performance
- Can be modified to accommodate different batch processing requirements 