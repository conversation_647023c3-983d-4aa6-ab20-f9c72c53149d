package com.enttribe.commons.ai.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;

import java.io.IOException;

/**
 * Custom JSON deserializer for Spring AI Message objects.
 * This deserializer handles the conversion of JSON data into appropriate Message implementations
 * based on the messageType field in the JSON structure.
 *
 * <p>Supported message types:
 * <ul>
 *     <li>USER - Deserializes into {@link UserMessage}</li>
 *     <li>ASSISTANT - Deserializes into {@link AssistantMessage}</li>
 * </ul>
 *
 * <AUTHOR>
 * @see Message
 * @see UserMessage
 * @see AssistantMessage
 * @see JsonDeserializer
 */
public class MessageDeserializer extends JsonDeserializer<Message> {

    /**
     * Deserializes JSON content into a Message object.
     * The method expects a JSON structure containing 'messageType' and 'content' fields.
     *
     * @param parser  The JsonParser used for reading JSON content
     * @param context Context for the process of deserialization
     * @return A Message implementation (either UserMessage or AssistantMessage) based on the messageType
     * @throws IOException              If there is a problem reading from the JsonParser
     * @throws IllegalArgumentException If an unknown message type is encountered
     */
    @Override
    public Message deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        JsonNode node = parser.getCodec().readTree(parser);

        // Determine the type of message
        String messageType = node.get("messageType").asText();
        String content = node.get("text").asText();

        // Deserialize based on the messageType
        if ("USER".equalsIgnoreCase(messageType)) {
            return new UserMessage(content);
        } else if ("ASSISTANT".equalsIgnoreCase(messageType)) {
            return new AssistantMessage(content);
        } else {
            throw new IllegalArgumentException("Unknown message type: " + messageType);
        }
    }

}
