package com.enttribe.commons.ai.knowledge_base;

import com.enttribe.commons.ai.model.KnowledgeBase;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import com.enttribe.commons.ai.rag.VectorService;
import com.enttribe.commons.ai.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


public class SchemaProviderTool implements Function<SchemaProviderTool.Request, SchemaProviderTool.Response> {

    private static final Logger log = LoggerFactory.getLogger(SchemaProviderTool.class);
    private final VectorService vectorService;
    private final VectorMetaData vectorMetaData;

    public SchemaProviderTool(VectorService vectorService, KnowledgeBase knowledgeBase) {
        String metaData = knowledgeBase.getVectorMetaData();
        log.info("initializing SchemaProvider tool. metaData : {}", metaData);
        this.vectorService = vectorService;
        this.vectorMetaData = VectorMetaData.fromMap(getMetaDataMap(metaData));
    }

    /**
     * Processes a question and returns a response based on the knowledge base content.
     * This method performs a vector search using the provided question and returns
     * the most relevant answer found in the document store.
     *
     * @param request The request containing the question to be answered
     * @return A Response object containing the answer retrieved from the knowledge base
     */
    @Override
    public Response apply(Request request) {
        log.info("inside function KnowledgeBase. request : {}", request.tableName());
        String filterExpression = String.format("'doc_id' == '%s'", request.tableName());
        log.info("filterExpression for SchemaProviderTool : {}", filterExpression);
        SearchRequest searchRequest = SearchRequest.builder()
                .filterExpression(filterExpression)
                .query(request.tableName()).build();

        List<Document> documents = vectorService.similaritySearch(vectorMetaData, searchRequest);
        String tableSchema = documents.stream().map(Document::getText).collect(Collectors.joining());
        return new Response(tableSchema);
    }

    /**
     * Record representing a tableName request to the knowledge base.
     *
     * @param tableName The table name for which schema will be provided.
     */
    public record Request(@JsonProperty("The table name for which schema will be provided") String tableName) {
    }

    /**
     * Record representing the response from the knowledge base.
     *
     * @param result The answer retrieved from the knowledge base
     */
    public record Response(@JsonProperty("result") String result) {
    }

    /**
     * Converts a JSON metadata string into a Map representation.
     *
     * @param metaData JSON string containing metadata
     * @return Map containing the parsed metadata
     * @throws RuntimeException if the JSON parsing fails
     */
    private Map<String, String> getMetaDataMap(String metaData) {
        ObjectMapper objectMapper = JsonUtils.getObjectMapper();
        try {
            return objectMapper.readValue(metaData, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}