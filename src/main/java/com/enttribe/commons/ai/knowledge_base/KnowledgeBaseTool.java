package com.enttribe.commons.ai.knowledge_base;

import com.enttribe.commons.ai.model.KnowledgeBase;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import com.enttribe.commons.ai.rag.VectorService;
import com.enttribe.commons.ai.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.vectorstore.SearchRequest;

import java.util.Map;
import java.util.function.Function;

/**
 * A tool for interacting with a knowledge base through vector-based search and retrieval.
 * This class provides functionality to search and retrieve answers from a document store
 * based on vector similarity and metadata filtering.
 *
 * <p>The tool uses a vector service to perform semantic searches and retrieve relevant
 * information based on input questions. It can be configured with specific metadata
 * and filter expressions to narrow down the search scope.</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class KnowledgeBaseTool implements Function<KnowledgeBaseTool.Request, KnowledgeBaseTool.Response> {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseTool.class);
    private final VectorService vectorService;
    private final VectorMetaData vectorMetaData;
    private final String filterExpression;
    private SearchRequest searchRequest;

    /**
     * Constructs a new KnowledgeBaseTool with the specified parameters.
     *
     * @param vectorService    The vector service implementation for performing searches
     * @param knowledgeBase    Knowledge Base object containing necessary information
     * @param filterExpression Expression used to filter search results
     */
    public KnowledgeBaseTool(VectorService vectorService, KnowledgeBase knowledgeBase, String filterExpression) {
        String metaData = knowledgeBase.getVectorMetaData();
        log.info("initializing KnowledgeBase tool. metaData : {}", metaData);
        this.vectorService = vectorService;
        this.filterExpression = filterExpression;
        this.vectorMetaData = VectorMetaData.fromMap(getMetaDataMap(metaData));
        this.searchRequest = SearchRequest.builder()
                .similarityThreshold(knowledgeBase.getSimilarityThreshold())
                .topK(knowledgeBase.getTopK()).build();
        log.info("KnowledgeBase tool initialized with filterExpression : {}", filterExpression);
    }

    /**
     * Processes a question and returns a response based on the knowledge base content.
     * This method performs a vector search using the provided question and returns
     * the most relevant answer found in the document store.
     *
     * @param request The request containing the question to be answered
     * @return A Response object containing the answer retrieved from the knowledge base
     */
    @Override
    public Response apply(Request request) {
        log.info("inside function KnowledgeBase. request : {}", request.question());
        if (filterExpression != null) {
            searchRequest = SearchRequest.from(searchRequest).filterExpression(filterExpression).build();
        }
        String answer = vectorService.getAnswerFromDocument(vectorMetaData, request.question(), searchRequest);
        return new Response(answer);
    }

    /**
     * Record representing a question request to the knowledge base.
     *
     * @param question The question to be answered
     */
    public record Request(@JsonProperty("question") String question) {
    }

    /**
     * Record representing the response from the knowledge base.
     *
     * @param result The answer retrieved from the knowledge base
     */
    public record Response(@JsonProperty("result") String result) {
    }

    /**
     * Converts a JSON metadata string into a Map representation.
     *
     * @param metaData JSON string containing metadata
     * @return Map containing the parsed metadata
     * @throws RuntimeException if the JSON parsing fails
     */
    private Map<String, String> getMetaDataMap(String metaData) {
        ObjectMapper objectMapper = JsonUtils.getObjectMapper();
        try {
            return objectMapper.readValue(metaData, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}
