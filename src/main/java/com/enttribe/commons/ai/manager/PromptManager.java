package com.enttribe.commons.ai.manager;

import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.prompt.MessageDto;
import com.enttribe.commons.ai.service.PromptApi;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Manager class responsible for handling and managing AI prompts within the application.
 * This class provides functionality to load, cache, and retrieve prompt models that are used
 * for AI interactions. It ensures that prompts are properly loaded at application startup
 * and maintains them in memory for efficient access.
 *
 * <p>The manager uses a static cache to store prompt models and provides methods to access
 * and refresh these prompts as needed. It also handles the transformation of prompt variable
 * syntax to maintain consistency across the application.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class PromptManager {

    @Value("${commons.ai.sdk.app.name}")
    private String applicationName;

    private static final Logger log = LoggerFactory.getLogger(PromptManager.class);
    private static volatile Map<String, PromptModel> prompts = new HashMap<>();

    private final PromptApi promptApi;

    public PromptManager(PromptApi promptApi) {
        this.promptApi = promptApi;
    }

    /**
     * Loads and refreshes the prompts from the configured prompt API.
     * This method is called automatically after bean construction and can be used
     * to manually refresh the prompts if needed.
     *
     * <p>The method performs the following operations:
     * <ul>
     *     <li>Fetches prompts for the configured application</li>
     *     <li>Modifies prompt variable syntax for consistency</li>
     *     <li>Updates the internal prompt cache</li>
     * </ul>
     *
     * <p>If no prompts are found for the application, a warning is logged.
     */
    @PostConstruct
    public void loadPrompts() {
        log.info("refreshing prompts...");
        List<PromptModel> promptModels = promptApi.getPromptsOfApplication(applicationName);
        if (promptModels.isEmpty()) {
            log.warn("prompts are not loaded");
            return;
        }
        modifyPromptVariableSyntax(promptModels);
        Map<String, PromptModel> promptsTempMap = getPromptMap(promptModels);
        log.info("prompts for application {} loaded successfully", applicationName);
        log.info("total {} prompts are loaded", promptsTempMap.size());
        log.info("loaded prompts are {}", promptsTempMap.keySet());

        if (!promptsTempMap.isEmpty()) {
            log.info("updating prompt map");
            PromptManager.prompts = Map.copyOf(promptsTempMap);
            log.info("prompts updated successfully");
        } else {
            log.warn("prompt map is empty");
        }
    }

    /**
     * Retrieves a prompt model by its key identifier.
     *
     * @param key The unique identifier of the prompt model to retrieve
     * @return The {@link PromptModel} associated with the given key
     * @throws IllegalArgumentException if prompts are not loaded, or if no prompt is found for the given key,
     *                                  or if the provider is not specified for the prompt
     */
    public PromptModel getPromptModel(String key) {
        if (prompts.isEmpty()) loadPrompts();
        Assert.notEmpty(prompts, String.format("prompts are not loaded for application : %s", applicationName));
        PromptModel promptModel = prompts.get(key);
        Assert.notNull(promptModel, String.format("no prompt model found for prompt-id: %s", key));
        Assert.hasText(promptModel.getProvider(), String.format("provider is not associated with prompt-id : %s", key));
        return promptModel;
    }

    /**
     * Converts a list of prompt models into a map for easier access.
     *
     * @param promptModels List of prompt models to convert
     * @return A map with prompt IDs as keys and PromptModel objects as values
     */
    private Map<String, PromptModel> getPromptMap(List<PromptModel> promptModels) {
        return promptModels.stream().collect(Collectors.toMap(
                PromptModel::getPromptId,    // Key mapper: uses the `id` as the key
                model -> model,   // Value mapper: uses the `PromptModel` object as the value
                (existingValue, newValue) -> newValue
        ));
    }

    /**
     * Modifies the variable syntax in prompt messages to ensure consistency.
     * Converts double curly braces {{variable}} to single curly braces {variable}.
     *
     * @param promptModels List of prompt models whose messages need to be processed
     */
    private void modifyPromptVariableSyntax(List<PromptModel> promptModels) {
        if (promptModels == null || promptModels.isEmpty()) {
            return; // Nothing to process
        }

        // Iterate over each PromptModel
        for (PromptModel promptModel : promptModels) {
            if (promptModel.getMessages() != null) {
                // Iterate over each message in the PromptModel
                for (MessageDto messageDto : promptModel.getMessages()) {
                    if (messageDto.getContent() != null) {
                        // Replace double curly braces with single braces
                        String updatedContent = messageDto.getContent()
                                .replaceAll("\\{\\{", "{")
                                .replaceAll("}}", "}");
                        messageDto.setContent(updatedContent);
                    }
                }
            }
        }
    }

}


