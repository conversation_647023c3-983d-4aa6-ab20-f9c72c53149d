package com.enttribe.commons.ai.manager;

import com.enttribe.commons.ai.model.rag.VectorMetaData;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Manager class responsible for handling vector metadata storage and retrieval in the knowledge base.
 * This component maintains a mapping between conversation IDs and their associated vector metadata,
 * facilitating efficient retrieval and storage of vector-based information.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class KnowledgeBaseManager {

    private static final Map<String, VectorMetaData> knowledgeBaseMap = new HashMap<>();

    /**
     * Adds vector metadata to the knowledge base for a specific conversation.
     *
     * @param conversationId the unique identifier for the conversation
     * @param metaData       the vector metadata to be stored
     */
    public void addVectorMetaData(String conversationId, VectorMetaData metaData) {
        knowledgeBaseMap.put(conversationId, metaData);
    }

    /**
     * Retrieves vector metadata from the knowledge base for a specific conversation.
     *
     * @param conversationId the unique identifier for the conversation
     * @return the vector metadata associated with the conversation ID, or null if not found
     */
    public VectorMetaData getVectorMetaData(String conversationId) {
        return knowledgeBaseMap.get(conversationId);
    }

}
