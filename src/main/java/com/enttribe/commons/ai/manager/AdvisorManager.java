package com.enttribe.commons.ai.manager;

import com.enttribe.commons.ai.advisor.MessageChatMemoryAdvisor;
import com.enttribe.commons.ai.advisor.RedisChatRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Manager class responsible for handling chat memory advisors and memory implementations.
 * This class provides a flexible configuration for chat memory storage, supporting both
 * in-memory and Redis-based storage solutions based on the availability of Redis configuration.
 * The class automatically determines the appropriate chat memory implementation:
 * - If Redis is configured (RedisTemplate is available), it uses RedisChatMemory
 * - If Redis is not configured, it falls back to InMemoryChatMemory
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class AdvisorManager {

    private static final Logger log = LoggerFactory.getLogger(AdvisorManager.class);
    private final Advisor chatMemoryAdvisor;
    private final ChatMemory chatMemory;

    /**
     * Constructs an AdvisorManager with the appropriate chat memory implementation.
     *
     * @param redisTemplate The Redis template for Redis-based storage. Can be null, in which case
     *                      in-memory storage will be used. This parameter is autowired but optional.
     */
    public AdvisorManager(@Autowired(required = false) RedisTemplate<String, Object> redisTemplate) {
        if (redisTemplate == null) {
            log.info("redis template is null, configuring InMemoryChatMemoryRepository");
            this.chatMemory = MessageWindowChatMemory.builder()
                    .chatMemoryRepository(new InMemoryChatMemoryRepository())
                    .maxMessages(25)
                    .build();
        } else {
            log.info("redis template is not null, configuring RedisChatMemory");
            this.chatMemory = MessageWindowChatMemory.builder()
                    .chatMemoryRepository(new RedisChatRepository(redisTemplate))
                    .maxMessages(25)
                    .build();
        }

        this.chatMemoryAdvisor = MessageChatMemoryAdvisor.builder(chatMemory).order(899).build();
    }

    /**
     * Retrieves the configured chat memory advisor.
     *
     * @return The configured {@link Advisor} instance for chat memory management
     */
    public Advisor getChatMemoryAdvisor() {
        return chatMemoryAdvisor;
    }

    /**
     * Retrieves the configured chat memory implementation.
     *
     * @return The configured {@link ChatMemory} instance, either Redis-based or in-memory
     */
    public ChatMemory getChatMemory() {
        return chatMemory;
    }

}
