package com.enttribe.commons.ai.test;

import com.enttribe.commons.ai.advisor.AuditAdvisor;
import com.enttribe.commons.ai.chat.AiAgentModel;
import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.model.agent.AgentPromptV1;
import com.enttribe.commons.ai.rag.VectorService;
import com.enttribe.commons.ai.service.PromptApi;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/test")
public class Test {

    @Autowired
    private PromptApi promptApi;

    @Autowired
    private ChatModel chatModel;

    @Autowired(required = false)
    private ChatClient chatClient;

    private final AiChatModel aiChatModel;

    @Autowired
    private AiAgentModel aiAgentModel;

    @Autowired(required = false)
    private InferenceManager inferenceManager;

    @Autowired
    private VectorService vectorService;


    public Test(AiChatModel aiChatModel, ChatModel chatModel) {
        this.aiChatModel = aiChatModel;

        this.chatClient = ChatClient.builder(chatModel)
//                .defaultAdvisors(new PromptChatMemoryAdvisor(new RedisChatMemory(redisTemplate, objectMapper)))
                .build();
    }

    @PostMapping("/1")
    public Object test(@RequestBody Map<String, Object> request) {
        String question = (String) request.get("question");

        AgentPromptV1 agentPromptV1 = AgentPromptV1.builder()
                .promptId("1")
                .agentName("Test")
                .knowledgeBaseIds(List.of("123"))
                .conversationId("testing")
                .build();

        ChatClient chatClient1 = aiAgentModel.getChatClientV1(agentPromptV1);

        String content = chatClient1.prompt()
                .user(question)
                .call()
                .content();

        return content;


    }


    public static void main(String[] args) {

        String str = """
                Hi {name}
                """;
        String template = """
                This is prompt.
                Give response in following JSON format
                \\{
                    "result" : "<output>"
                \\}
                """;
        PromptTemplate promptTemplate = new PromptTemplate(template);
        System.out.println(promptTemplate.render());

    }


}
