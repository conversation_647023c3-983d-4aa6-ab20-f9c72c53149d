package com.enttribe.commons.ai.rag;

import com.enttribe.commons.ai.advisor.QuestionAnswerAdvisor;
import com.enttribe.commons.ai.config.VectorStoreConfig;
import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

@Service
public class VectorServiceImpl implements VectorService {

    @Value("${commons.ai.sdk.vector.max.token.per_document:50000}")
    private Integer maxTokenPerDocument;

    @Value("${commons.ai.sdk.vector.max.document.per_request:50}")
    private Integer maxDocumentsPerRequest;

    private final InferenceManager inferenceManager;
    private final VectorStoreConfig vectorStoreConfig;

    public VectorServiceImpl(InferenceManager inferenceManager, VectorStoreConfig vectorStoreConfig) {
        this.inferenceManager = inferenceManager;
        this.vectorStoreConfig = vectorStoreConfig;
    }

    @Override
    public String saveDocuments(VectorMetaData metaData, Document document) {
        validate(document);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(metaData);
        vectorStore.accept(List.of(document));
        return document.getId();
    }

    @Override
    public void saveDocuments(VectorMetaData metaData, List<Document> documents) {
        validate(documents);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(metaData);
        vectorStore.accept(documents);
    }

    @Override
    public List<Document> similaritySearch(VectorMetaData metaData, SearchRequest searchRequest) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(metaData);
        return vectorStore.similaritySearch(searchRequest);
    }

    @Override
    public void deleteDocuments(VectorMetaData metaData, List<String> documentIds) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(metaData);
        vectorStore.delete(documentIds);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, List<String> documentIds) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        vectorStore.delete(documentIds);
    }

    @Override
    public String saveDocument(String vectorStoreId, Document document) {
        validate(document);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        vectorStore.accept(List.of(document));
        return document.getId();
    }

    @Override
    public void saveDocuments(String vectorStoreId, List<Document> documents) {
        validate(documents);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        vectorStore.accept(documents);
    }

    @Override
    public List<Document> similaritySearch(String vectorStoreId, SearchRequest searchRequest) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        return vectorStore.similaritySearch(searchRequest);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, String filterExpression) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        vectorStore.delete(filterExpression);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, Map<String, Object> filterMap) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        String filterExpression = createFilterExpression(filterMap);
        vectorStore.delete(filterExpression);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, Filter.Expression filterExpression) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId);
        vectorStore.delete(filterExpression);
    }

    @Override
    public String getAnswerFromDocument(VectorMetaData metaData, String question, SearchRequest searchRequest) {
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(metaData);
        ChatModel chatModel = inferenceManager.getChatModelByProvider(metaData.getProvider());

        OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
                .model(metaData.getChatModel())
                .build();

        ChatResponse chatResponse = ChatClient.builder(chatModel)
                .build().prompt()
                .user(question)
                .options(chatOptions)
                .advisors(new QuestionAnswerAdvisor(vectorStore, searchRequest))
                .call()
                .chatResponse();

        return chatResponse.getResult().getOutput().getText();
    }

    private void validate(List<Document> documents) {
        Assert.notNull(documents, "document must not be null");
        Assert.state(documents.size() <= maxDocumentsPerRequest,
                String.format("max %s documents are allowed per request. You can modify the limit by updating 'commons.ai.sdk.vector.max.document.per_request' property", maxDocumentsPerRequest)
        );
        for (Document document : documents) {
            Assert.notNull(document.getText(), "document text must not be null");
            if (document.getText().length() > maxTokenPerDocument) {
                throw new UnsupportedOperationException(
                        String.format("single document must not cross %s token limit. You can modify the limit by updating 'commons.ai.sdk.vector.max.token.per_document' property", maxTokenPerDocument)
                );
            }
        }
    }


    private void validate(Document document) {
        Assert.notNull(document, "document must not be null");
        Assert.notNull(document.getText(), "document text must not be null");
        if (document.getText().length() > maxTokenPerDocument) {
            throw new UnsupportedOperationException(
                    String.format("single document must not cross %s token limit. You can modify the limit by updating 'commons.ai.sdk.vector.max.token.per_document' property", maxTokenPerDocument)
            );
        }
    }

    private String createFilterExpression(Map<String, Object> metadata) {
        StringBuilder expression = new StringBuilder();

        for (Map.Entry<String, Object> entry : metadata.entrySet()) {
            if (!expression.isEmpty()) {
                expression.append(" and ");
            }
            String key = entry.getKey();
            Object value = entry.getValue();

            expression.append("\"").append(key).append("\" == ");

            if (value instanceof String) {
                expression.append("\"").append(value).append("\"");
            } else if (value instanceof Number) {
                expression.append(value);
            }
        }
        return expression.toString();
    }

}
