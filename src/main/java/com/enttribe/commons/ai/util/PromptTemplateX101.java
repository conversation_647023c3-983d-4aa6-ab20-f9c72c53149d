package com.enttribe.commons.ai.util;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.antlr.runtime.Token;
import org.antlr.runtime.TokenStream;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplateActions;
import org.springframework.ai.chat.prompt.PromptTemplateMessageActions;
import org.springframework.ai.content.Media;
import org.springframework.core.io.Resource;
import org.springframework.util.StreamUtils;
import org.stringtemplate.v4.ST;

public class PromptTemplateX101 implements PromptTemplateActions, PromptTemplateMessageActions {
    protected String template;
    private ST st;
    private Map<String, Object> dynamicModel;

    public PromptTemplateX101(Resource resource) {
        this.dynamicModel = new HashMap();

        try (InputStream inputStream = resource.getInputStream()) {
            this.template = StreamUtils.copyToString(inputStream, Charset.defaultCharset());
        } catch (IOException ex) {
            throw new RuntimeException("Failed to read resource", ex);
        }

        try {
            this.st = new ST(this.template, '{', '}');
        } catch (Exception ex) {
            throw new IllegalArgumentException("The template string is not valid.", ex);
        }
    }

    public PromptTemplateX101(String template) {
        this.dynamicModel = new HashMap();
        this.template = template;

        try {
            this.st = new ST(this.template, '{', '}');
        } catch (Exception ex) {
            throw new IllegalArgumentException("The template string is not valid.", ex);
        }
    }

    public PromptTemplateX101(String template, Map<String, Object> model) {
        this.dynamicModel = new HashMap();
        this.template = template;

        try {
            this.st = new ST(this.template, '{', '}');

            for(Map.Entry<String, Object> entry : model.entrySet()) {
                this.add((String)entry.getKey(), entry.getValue());
            }

        } catch (Exception ex) {
            throw new IllegalArgumentException("The template string is not valid.", ex);
        }
    }

    public PromptTemplateX101(Resource resource, Map<String, Object> model) {
        this.dynamicModel = new HashMap();

        try (InputStream inputStream = resource.getInputStream()) {
            this.template = StreamUtils.copyToString(inputStream, Charset.defaultCharset());
        } catch (IOException ex) {
            throw new RuntimeException("Failed to read resource", ex);
        }

        try {
            this.st = new ST(this.template, '{', '}');

            for(Map.Entry<String, Object> entry : model.entrySet()) {
                this.add((String)entry.getKey(), entry.getValue());
            }

        } catch (Exception ex) {
            throw new IllegalArgumentException("The template string is not valid.", ex);
        }
    }

    public void add(String name, Object value) {
        this.st.add(name, value);
        this.dynamicModel.put(name, value);
    }

    public String getTemplate() {
        return this.template;
    }


    public String render() {
        this.validate(this.dynamicModel);
        return this.st.render();
    }

    public String render(Map<String, Object> model) {
        this.validate(model);

        for(Map.Entry<String, Object> entry : model.entrySet()) {
            if (this.st.getAttribute((String)entry.getKey()) != null) {
                this.st.remove((String)entry.getKey());
            }

            if (entry.getValue() instanceof Resource) {
                this.st.add((String)entry.getKey(), this.renderResource((Resource)entry.getValue()));
            } else {
                this.st.add((String)entry.getKey(), entry.getValue());
            }
        }

        return this.st.render();
    }

    private String renderResource(Resource resource) {
        try {
            return resource.getContentAsString(Charset.defaultCharset());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public Message createMessage() {
        return new UserMessage(this.render());
    }

    @Override
    public Message createMessage(List<Media> mediaList) {
        return null;
    }


    public Message createMessage(Map<String, Object> model) {
        return new UserMessage(this.render(model));
    }

    public Prompt create() {
        return new Prompt(this.render(new HashMap<>()));
    }

    public Prompt create(ChatOptions modelOptions) {
        return new Prompt(this.render(new HashMap<>()), modelOptions);
    }

    public Prompt create(Map<String, Object> model) {
        return new Prompt(this.render(model));
    }

    public Prompt create(Map<String, Object> model, ChatOptions modelOptions) {
        return new Prompt(this.render(model), modelOptions);
    }

    public Set<String> getInputVariables() {
        TokenStream tokens = this.st.impl.tokens;
        Set<String> inputVariables = new HashSet<>();
        boolean isInsideList = false;

        for(int i = 0; i < tokens.size(); ++i) {
            Token token = tokens.get(i);
            if (token.getType() == 23 && i + 1 < tokens.size() && tokens.get(i + 1).getType() == 25) {
                if (i + 2 < tokens.size() && tokens.get(i + 2).getType() == 13) {
                    inputVariables.add(tokens.get(i + 1).getText());
                    isInsideList = true;
                }
            } else if (token.getType() == 24) {
                isInsideList = false;
            } else if (!isInsideList && token.getType() == 25) {
                inputVariables.add(token.getText());
            }
        }

        return inputVariables;
    }

    private Set<String> getModelKeys(Map<String, Object> model) {
        Set<String> dynamicVariableNames = new HashSet<>(this.dynamicModel.keySet());
        Set<String> modelVariables = new HashSet<>(model.keySet());
        modelVariables.addAll(dynamicVariableNames);
        return modelVariables;
    }

    protected void validate(Map<String, Object> model) {
        Set<String> templateTokens = this.getInputVariables();
        Set<String> modelKeys = this.getModelKeys(model);
        if (!modelKeys.containsAll(templateTokens)) {
            templateTokens.removeAll(modelKeys);
            throw new IllegalStateException("Not all template variables were replaced. Missing variable names are " + String.valueOf(templateTokens));
        }
    }
}
