package com.enttribe.commons.ai.util;

import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;



/**
 * Encoder/Decoder based on AES.
 *
 * <AUTHOR>
 */
public class AESUtils {


    private static final int KEY_SIZE = 128;
    private static final int ITERATION_COUNT = 10000;
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String SECRET_KEY_ALGORITHM = "PBKDF2WithHmacSHA256";
    private static final String PASSPHRASE = getDecodedPassphrase();
    public static String E_C = System.getenv("E_C");
    private static final String STATIC_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String UTF_8 = "UTF-8";
    private static final SecretKeySpec STATIC_KEY_SPEC;
    private static final Cipher ENCRYPT_CIPHER;
    private static final Cipher DECRYPT_CIPHER;
    private static final Object LOCK = new Object();


    static {
        try {
            String staticSecretKey = decryptE_C(E_C);
            STATIC_KEY_SPEC = new SecretKeySpec(staticSecretKey.getBytes(UTF_8), "AES");

            ENCRYPT_CIPHER = Cipher.getInstance(STATIC_CIPHER_ALGORITHM);
            ENCRYPT_CIPHER.init(Cipher.ENCRYPT_MODE, STATIC_KEY_SPEC);

            DECRYPT_CIPHER = Cipher.getInstance(STATIC_CIPHER_ALGORITHM);
            DECRYPT_CIPHER.init(Cipher.DECRYPT_MODE, STATIC_KEY_SPEC);
        } catch (Exception e) {
            throw new ExceptionInInitializerError("Failed to initialize encryption: " + e.getMessage());
        }
    }


    private AESUtils() {
    }

    protected static String getDecodedPassphrase() {
        byte[] encodedBytes = {86, 71, 104, 112, 99, 121, 66, 112, 99, 121, 66, 109, 98, 51, 74, 108, 99, 50, 108, 110, 97,
                72, 81, 103, 90, 109, 57, 121, 73, 71, 86, 117, 89, 51, 74, 53, 99, 72, 81, 103, 89, 87, 53, 107, 73, 71, 82,
                108, 89, 51, 74, 53, 99, 72, 81, 61};
        return new String(Base64.getDecoder().decode(encodedBytes));
    }


    private static String encryptE_C(String plainText) throws Exception {
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[16]; // 16 bytes for a 128-bit AES block size
        byte[] salt = new byte[16];
        secureRandom.nextBytes(iv);
        secureRandom.nextBytes(salt);

        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec secretKeySpec = generateSecretKeySpec(salt);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes("UTF-8"));

        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String saltBase64 = Base64.getEncoder().encodeToString(salt);
        String encryptedBase64 = Base64.getEncoder().encodeToString(encryptedBytes);

        return ivBase64 + ":" + saltBase64 + ":" + encryptedBase64;
    }

    private static String decryptE_C(String cipherText) throws Exception {
        String[] parts = cipherText.split(":");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid encrypted text format");
        }

        byte[] iv = Base64.getDecoder().decode(parts[0]);
        byte[] salt = Base64.getDecoder().decode(parts[1]);
        byte[] encryptedBytes = Base64.getDecoder().decode(parts[2]);

        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        SecretKeySpec secretKeySpec = generateSecretKeySpec(salt);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, "UTF-8");
    }



    public static String encrypt(String plainText) throws Exception {
        return encryptWithStaticKey(plainText);
    }

    public static String decrypt(String cipherText) throws Exception {
        return decryptWithStaticKey(cipherText);
    }

    private static SecretKeySpec generateSecretKeySpec(byte[] salt) throws Exception {
        KeySpec keySpec = new PBEKeySpec(PASSPHRASE.toCharArray(), salt, ITERATION_COUNT, KEY_SIZE);
        SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance(SECRET_KEY_ALGORITHM);
        byte[] keyBytes = secretKeyFactory.generateSecret(keySpec).getEncoded();
        return new SecretKeySpec(keyBytes, "AES");
    }


    public static String encryptWithStaticKey(String plainText) throws Exception {
        if (plainText == null) {
            return null;
        }

        byte[] encryptedBytes;
        synchronized (LOCK) {
            encryptedBytes = ENCRYPT_CIPHER.doFinal(plainText.getBytes(UTF_8));
        }
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static String decryptWithStaticKey(String cipherText) throws Exception {
        if (cipherText == null) {
            return null;
        }

        byte[] decryptedBytes;
        synchronized (LOCK) {
            decryptedBytes = DECRYPT_CIPHER.doFinal(Base64.getDecoder().decode(cipherText));
        }
        return new String(decryptedBytes, UTF_8);
    }


}
