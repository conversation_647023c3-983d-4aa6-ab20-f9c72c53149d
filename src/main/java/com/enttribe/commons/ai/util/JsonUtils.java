package com.enttribe.commons.ai.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * A utility class that provides JSON serialization and deserialization functionality using Jackson ObjectMapper.
 * This class offers methods to convert Java objects to JSON strings and vice versa, handling both single objects
 * and collections. It uses a singleton pattern for the ObjectMapper instance to improve performance and reduce
 * resource usage.
 * Features:
 * <ul>
 *   <li>Thread-safe singleton ObjectMapper instance</li>
 *   <li>Configurable JSON processing with fail-safe options</li>
 *   <li>Support for Java 8 date/time types through JavaTimeModule</li>
 *   <li>Handles unknown properties gracefully</li>
 *   <li>Provides both object and collection conversion utilities</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class JsonUtils {

    private JsonUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);

    // Single, static, and final ObjectMapper instance
    private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

    /**
     * Creates and configures a new ObjectMapper instance with custom settings.
     * Configuration includes:
     * <ul>
     *   <li>Disabled failure on empty beans</li>
     *   <li>Disabled failure on unknown properties</li>
     *   <li>Disabled writing dates as timestamps</li>
     *   <li>Registered JavaTimeModule for proper date/time handling</li>
     * </ul>
     *
     * @return configured ObjectMapper instance
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    /**
     * Returns the singleton instance of ObjectMapper used for all JSON operations.
     * This method ensures that only one ObjectMapper instance is created and reused
     * throughout the application lifecycle, which is both thread-safe and efficient.
     *
     * @return the singleton ObjectMapper instance
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * Converts a Java object to its JSON string representation.
     * This method handles the serialization of any Java object to a JSON string format.
     * If an error occurs during conversion, it will be logged and an empty string will be returned.
     *
     * @param object the Java object to be converted to JSON string
     * @return the JSON string representation of the object, or an empty string if conversion fails
     */
    public static String convertToJSON(Object object) {
        try {
            ObjectMapper mapper = getObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Error inside @method convertToJSON. Exception message : {}", e.getMessage());
            return "";
        }
    }

    /**
     * Converts a JSON string to a Java object of the specified type.
     * This method performs deserialization of JSON data into a concrete Java class.
     * It uses the configured ObjectMapper which ignores unknown properties in the JSON string.
     *
     * @param <T>        the type parameter representing the target Java class
     * @param jsonString the JSON string to be converted
     * @param clazz      the Class object representing the target type
     * @return an instance of type T containing the deserialized data
     * @throws JsonProcessingException if the JSON string cannot be parsed or is invalid
     */
    public static <T> T convertJsonToObject(String jsonString, Class<T> clazz) throws JsonProcessingException {
        ObjectMapper objectMapper = getObjectMapper();
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            log.error("Error inside @method convertJsonToObject. Exception message : {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Converts a JSON array string to a List of objects of the specified type.
     * This method handles the deserialization of JSON arrays into Java List objects.
     * It uses Jackson's TypeFactory to create the appropriate collection type for deserialization.
     *
     * @param <T>        the type parameter representing the element type of the list
     * @param jsonString the JSON array string to be converted
     * @param clazz      the Class object representing the element type
     * @return a List of objects of type T containing the deserialized data
     * @throws JsonProcessingException if the JSON string cannot be parsed or is invalid
     */
    public static <T> List<T> convertJsonToList(String jsonString, Class<T> clazz) throws JsonProcessingException {
        ObjectMapper objectMapper = getObjectMapper();
        try {
            return objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            log.error("Error inside @method convertJsonToList. Exception message : {}", e.getMessage());
            throw e;
        }
    }

}
