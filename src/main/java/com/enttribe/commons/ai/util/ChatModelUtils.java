package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.prompt.MessageDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Utility class for handling chat model operations and message processing.
 * This class provides methods for preparing chat options, managing message specifications,
 * and handling various types of chat messages in the context of AI conversations.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ChatModelUtils {

    private static final Logger log = LoggerFactory.getLogger(ChatModelUtils.class);

    private ChatModelUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Prepares chat options based on the provided prompt model configuration.
     * Configures settings such as model, temperature, top-p, response format, and max tokens.
     * If tools are specified in the prompt model, they will be included in the chat options.
     *
     * @param promptModel The prompt model containing configuration settings
     * @return ChatOptions configured according to the prompt model specifications
     */
    public static ChatOptions prepareChatOptions(PromptModel promptModel) {
        ChatOptions chatOptions;
        ResponseFormat responseFormat = ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build();
        String reasoningEffort = promptModel.getReasoningEffort();

        if (CollectionUtils.isEmpty(promptModel.getTools())) {
            chatOptions = OpenAiChatOptions.builder()
                    .model(promptModel.getModel())
                    .temperature(promptModel.getTemperature())
                    .topP(promptModel.getTopP())
                    .responseFormat(promptModel.getJsonMode() ? responseFormat : null)
                    .reasoningEffort(reasoningEffort)
                    .maxTokens(promptModel.getMaxTokens())
                    .build();
        } else {
            chatOptions = OpenAiChatOptions.builder()
                    .model(promptModel.getModel())
                    .temperature(promptModel.getTemperature())
                    .toolNames(promptModel.getTools())
                    .topP(promptModel.getTopP())
                    .responseFormat(promptModel.getJsonMode() ? responseFormat : null)
                    .reasoningEffort(reasoningEffort)
                    .maxTokens(promptModel.getMaxTokens())
                    .build();
        }
        return chatOptions;
    }

    /**
     * Prepares chat options based on the provided prompt model configuration.
     * Configures settings such as model, temperature, top-p, response format, and max tokens.
     * If tools are specified in the prompt model, they will be included in the chat options.
     *
     * @param promptModel The prompt model containing configuration settings
     * @param model       the llm model (this is used when provided, else use the model of prompt)
     * @return ChatOptions configured according to the prompt model specifications
     */
    public static ChatOptions prepareChatOptions(PromptModel promptModel, String model) {
        ChatOptions chatOptions;
        model = model != null ? model : promptModel.getModel();
        log.info("model for chat options : {}", model);
        ResponseFormat responseFormat = ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build();
        String reasoningEffort = promptModel.getReasoningEffort();
        OpenAiChatOptions.Builder builder;
        if (CollectionUtils.isEmpty(promptModel.getTools())) {
//            chatOptions
            builder = OpenAiChatOptions.builder()
                    .model(model)
                    .temperature(promptModel.getTemperature())
                    .topP(promptModel.getTopP())
                    .responseFormat(promptModel.getJsonMode() ? responseFormat : null)
                    .reasoningEffort(reasoningEffort)
                    .maxTokens(promptModel.getMaxTokens());

        } else {
            builder =  OpenAiChatOptions.builder()
                    .model(model)
                    .temperature(promptModel.getTemperature())
                    .toolNames(promptModel.getTools())
                    .topP(promptModel.getTopP())
                    .responseFormat(promptModel.getJsonMode() ? responseFormat : null)
                    .reasoningEffort(reasoningEffort)
                    .maxTokens(promptModel.getMaxTokens());
        }
        if(model.toLowerCase().startsWith("allam")){
            return builder.toolChoice("none").build();
        }
        return builder.build();
    }

    /**
     * Creates a consumer for chat advisor specifications with the given chat ID.
     * Sets up parameters for chat memory conversation ID and response size.
     *
     * @param chatId The unique identifier for the chat conversation, defaults to "default" if null
     * @return Consumer<ChatClient.AdvisorSpec> configured with the specified chat parameters
     */
    public static Consumer<ChatClient.AdvisorSpec> getChatAdvisorSpec(String chatId, Integer chatSize) {
        return a -> a
                .param("chat_memory_conversation_id", chatId == null ? "default" : chatId)
                .param("chat_memory_response_size", chatSize);
    }

    /**
     * Resolves messages from a prompt model using the provided variable map.
     * Converts message DTOs to Spring AI Message objects with resolved variables.
     *
     * @param promptModel The prompt model containing message definitions
     * @param variableMap Map of variables to be resolved in the messages
     * @return List of resolved Message objects
     */
    public static List<Message> getResolvedMessages(PromptModel promptModel, Map<String, Object> variableMap) {
        List<MessageDto> messageDtos = promptModel.getMessages();
        return messageDtos.stream().map(messageDto -> prepareMessage(messageDto, variableMap)).toList();
    }

    /**
     * Retrieves and resolves the system message from a prompt model.
     * Specifically looks for a message with the "system" role and resolves its variables.
     *
     * @param promptModel The prompt model containing the system message
     * @param variableMap Map of variables to be resolved in the system message
     * @return SystemMessage The resolved system message
     * @throws IllegalStateException if no system message is found
     */
    public static SystemMessage getSystemMessageX101(PromptModel promptModel, Map<String, Object> variableMap) {
        List<MessageDto> messageDtos = promptModel.getMessages();
        Assert.notEmpty(messageDtos, "no message is found for promptId " + promptModel.getPromptId());

        MessageDto systemMessageDto = messageDtos.stream().filter(messageDto -> messageDto.getRole().equals("system")).findFirst().orElseThrow(() -> new IllegalStateException("system prompt does not exist"));
        String resolvedPrompt = TemplateUtils.getResolvedPrompt(systemMessageDto.getContent(), variableMap);
        return new SystemMessage(resolvedPrompt);
    }

    /**
     * Processes a prompt model to create a list of messages with a system message first.
     * Separates system message from other messages and combines them in the correct order.
     *
     * @param promptModel The prompt model containing message definitions
     * @param variableMap Map of variables to be resolved in the messages
     * @return List of Message objects with system message first
     * @throws IllegalStateException if required messages are missing
     */
    public static List<Message> getMessageListV1(PromptModel promptModel, Map<String, Object> variableMap) {
        List<MessageDto> messageDtos = promptModel.getMessages();
        Assert.notEmpty(messageDtos, "no message is found for promptId " + promptModel.getPromptId());

        MessageDto systemMessageDto = messageDtos.stream()
                .filter(messageDto -> messageDto.getRole().equals("system")).findFirst().orElse(null);
        Assert.notNull(systemMessageDto, "system message is null");

        List<MessageDto> messageDtoList = messageDtos.stream()
                .filter(messageDto -> !messageDto.getRole().equals("system")).toList();

        Message systemMessageWithoutVariable = prepareMessage(systemMessageDto);
        List<Message> otherMessages = convertToMessages(messageDtoList, variableMap);

        List<Message> result = new ArrayList<>();
        result.add(systemMessageWithoutVariable);
        result.addAll(otherMessages);
        return result;
    }

    /**
     * Converts a list of MessageDto objects to Message objects with variable resolution.
     *
     * @param messageDtos List of message DTOs to convert
     * @param variableMap Map of variables to resolve in the messages
     * @return List of converted Message objects
     */
    private static List<Message> convertToMessages(List<MessageDto> messageDtos, Map<String, Object> variableMap) {
        return messageDtos.stream().map(messageDto -> prepareMessage(messageDto, variableMap)).toList();
    }

    /**
     * Prepares a Message object from a MessageDto without variable resolution.
     *
     * @param messageDto The message DTO to convert
     * @return Converted Message object
     */
    private static Message prepareMessage(MessageDto messageDto) {
        return getMessage(messageDto.getContent(), messageDto.getRole());
    }

    /**
     * Prepares a Message object from a MessageDto with variable resolution.
     *
     * @param messageDto  The message DTO to convert
     * @param variableMap Map of variables to resolve in the message
     * @return Converted Message object with resolved variables
     */
    private static Message prepareMessage(MessageDto messageDto, Map<String, Object> variableMap) {
        String resolvedPrompt = TemplateUtils.getResolvedPrompt(messageDto.getContent(), variableMap);
        return getMessage(resolvedPrompt, messageDto.getRole());
    }

    /**
     * Creates a Message object of the appropriate type based on the content and role.
     *
     * @param content The content of the message
     * @param role    The role of the message (system, user, or assistant)
     * @return Message object of the appropriate type
     * @throws IllegalStateException if the role is not recognized
     */
    private static Message getMessage(String content, String role) {
        MessageType messageType = getMessageType(role);
        return switch (messageType) {
            case MessageType.SYSTEM -> new SystemMessage(content);
            case MessageType.USER -> new UserMessage(content);
            case MessageType.ASSISTANT -> new AssistantMessage(content);

            default -> throw new IllegalStateException("role must be one of 'system', 'user' or 'assistant");
        };
    }

    /**
     * Converts a role string to the corresponding MessageType.
     *
     * @param role The role string to convert (system, user, or assistant)
     * @return MessageType corresponding to the role
     * @throws IllegalStateException if the role is not recognized
     */
    private static MessageType getMessageType(String role) {
        return switch (role.toLowerCase()) {
            case "system" -> MessageType.SYSTEM;
            case "user" -> MessageType.USER;
            case "assistant" -> MessageType.ASSISTANT;

            default ->
                    throw new IllegalStateException("role must be one of 'system', 'user' or 'assistant. provided role is : " + role);
        };
    }

}
