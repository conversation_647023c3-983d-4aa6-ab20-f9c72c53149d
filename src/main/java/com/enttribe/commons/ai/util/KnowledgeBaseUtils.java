package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.commons.ai.knowledge_base.KnowledgeBaseReturnDirect;
import com.enttribe.commons.ai.knowledge_base.KnowledgeBaseTool;
import com.enttribe.commons.ai.knowledge_base.SchemaProviderTool;
import com.enttribe.commons.ai.model.KnowledgeBase;
import com.enttribe.commons.ai.rag.VectorService;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;

/**
 * Utility class for managing and interacting with Knowledge Base functionality.
 * This class provides methods for creating knowledge base tools and handling knowledge base variables.
 * It supports both static tool creation and dynamic code execution for knowledge bases.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class KnowledgeBaseUtils {


    /**
     * Creates a FunctionCallback for a given knowledge base that can be used as a tool.
     * This method wraps a KnowledgeBase into a functional tool that can be used for retrieving
     * and processing knowledge base information.
     *
     * @param knowledgeBase the knowledge base entity to create a tool for
     * @param vectorService the vector service used for processing knowledge base data
     * @return FunctionCallback configured with the knowledge base tool
     */
    public static ToolCallback getKnowledgeBaseTool(KnowledgeBase knowledgeBase, VectorService vectorService, AiChatModel aiChatModel) {

        String filterExpression = knowledgeBase.getFilter() != null ?
                String.format("doc_id == '%s'", knowledgeBase.getFilter()) : null;

        boolean returnDirect = knowledgeBase.getReturnDirect() != null && knowledgeBase.getReturnDirect();

        if (knowledgeBase.getType().equals("SQL")) {
            SchemaProviderTool schemaProviderTool = new SchemaProviderTool(vectorService, knowledgeBase);
            return FunctionToolCallback.builder(knowledgeBase.getName(), schemaProviderTool)
                    .inputType(SchemaProviderTool.Request.class)
                    .description(knowledgeBase.getDescription())
                    .build();
        } else {
            if (returnDirect) {
                KnowledgeBaseReturnDirect knowledgeBaseTool =
                        new KnowledgeBaseReturnDirect(vectorService, knowledgeBase, aiChatModel, filterExpression);
                return FunctionToolCallback.builder(knowledgeBase.getName(), knowledgeBaseTool)
                        .inputType(KnowledgeBaseReturnDirect.Request.class)
                        .description(knowledgeBase.getDescription())
                        .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
                        .build();
            }
            KnowledgeBaseTool knowledgeBaseTool = new KnowledgeBaseTool(vectorService, knowledgeBase, filterExpression);
            return FunctionToolCallback.builder(knowledgeBase.getName(), knowledgeBaseTool)
                    .inputType(KnowledgeBaseTool.Request.class)
                    .description(knowledgeBase.getDescription())
                    .build();
        }
    }

}
