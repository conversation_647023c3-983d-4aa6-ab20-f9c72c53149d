package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.exception.ErrorAuditException;
import com.enttribe.commons.ai.model.audit.ExceptionAudit;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Utility class providing exception handling and auditing functionality.
 * This class offers methods for extracting stack traces, method parameters,
 * and creating exception audit objects for logging and monitoring purposes.
 *
 * <p>The class includes functionality for:
 * <ul>
 *     <li>Extracting readable stack traces from exceptions</li>
 *     <li>Capturing method parameters from AspectJ join points</li>
 *     <li>Creating detailed exception audit objects for both regular and parsing exceptions</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ExceptionUtils {

    /**
     * Private constructor to prevent instantiation of utility class.
     *
     * @throws UnsupportedOperationException always, as this utility class should not be instantiated
     */
    private ExceptionUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Converts an exception's stack trace into a readable string format.
     * Each stack trace element is converted to a string and joined with newline characters.
     *
     * @param exception the exception for which the stack trace is required
     * @return a string representation of the exception's stack trace, with each element on a new line
     */
    public static String getStackTrace(Throwable exception) {
        return Arrays.stream(exception.getStackTrace())
                .map(StackTraceElement::toString)
                .collect(Collectors.joining("\n"));
    }

    /**
     * Extracts method parameter names and their corresponding values from an AspectJ join point.
     * Creates a map where keys are parameter names and values are the actual parameter values
     * passed to the method at runtime.
     *
     * @param joinPoint the AspectJ join point containing method execution information
     * @return a map containing method parameter names as keys and their values as values
     */
    public static Map<String, Object> getMethodParameterMap(JoinPoint joinPoint) {
        Map<String, Object> methodParams = new HashMap<>();

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = methodSignature.getParameterNames();
        Object[] paramValues = joinPoint.getArgs();

        if (paramNames != null && paramValues != null) {
            for (int i = 0; i < paramNames.length; i++) {
                methodParams.put(paramNames[i], paramValues[i]);
            }
        }
        return methodParams;
    }

    /**
     * Creates an exception audit object based on a join point and thrown exception.
     * The audit object includes details such as:
     * <ul>
     *     <li>Audit ID</li>
     *     <li>Exception message and stack trace</li>
     *     <li>Method name and parameters</li>
     *     <li>Additional context for ErrorAuditException instances</li>
     * </ul>
     *
     * @param joinPoint the AspectJ join point where the exception occurred
     * @param exception the thrown exception to be audited
     * @return an ExceptionAudit object containing detailed information about the exception
     */
    public static ExceptionAudit getExceptionAudit(JoinPoint joinPoint, Throwable exception) {
        Map<String, Object> methodParams = ExceptionUtils.getMethodParameterMap(joinPoint);
        String auditId = (String) methodParams.get("auditId");

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodName = methodSignature.toLongString();

        ExceptionAudit exceptionAudit = ExceptionAudit.builder()
                .auditId(AuditUtils.generateAuditId(auditId, null))
                .exceptionMessage(exception.getMessage())
                .exceptionTrace(ExceptionUtils.getStackTrace(exception))
                .methodName(methodName)
                .methodParameters(methodParams)
                .build();

        if (exception instanceof ErrorAuditException errorAuditException) {
            exceptionAudit.setApplicationName(errorAuditException.getApplicationName());
            exceptionAudit.setPromptId(errorAuditException.getPromptId());
        }

        exceptionAudit.getMethodParameters().remove("typeReference");
        return exceptionAudit;
    }

    /**
     * Creates an exception audit object specifically for parsing-related exceptions.
     * This method is particularly useful for logging and tracking issues related to
     * data parsing operations.
     *
     * @param exception       the parsing exception that occurred
     * @param jsonString      the JSON string that failed to parse
     * @param format          the expected format of the data
     * @param inference       the inference context or type
     * @param applicationName the name of the application where the exception occurred
     * @return an ExceptionAudit object containing detailed information about the parsing exception
     */
    public static ExceptionAudit getParseExceptionAudit(Throwable exception, String jsonString,
                                                        String format, String inference,
                                                        String applicationName) {
        Map<String, Object> identifier = Map.of("jsonString", jsonString, "format", format, "inference", inference);
        ExceptionAudit exceptionAudit = ExceptionAudit.builder()
                .applicationName(applicationName)
                .auditId(String.format("%s_%s", applicationName, UUID.randomUUID()))
                .exceptionMessage(exception.getMessage())
                .exceptionTrace(ExceptionUtils.getStackTrace(exception))
                .identifier(identifier)
                .build();

        if (exception instanceof ErrorAuditException errorAuditException) {
            exceptionAudit.setApplicationName(errorAuditException.getApplicationName());
            exceptionAudit.setPromptId(errorAuditException.getPromptId());
        }

        return exceptionAudit;
    }

}
