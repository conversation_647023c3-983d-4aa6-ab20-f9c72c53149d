package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.prompt.MessageDto;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * A utility class that deals with prompt template.
 * <AUTHOR>
 */
public final class TemplateUtils {

    private TemplateUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Resolves a prompt by replacing dynamic variables with their corresponding values from the model map.
     *
     * @param prompt the prompt containing dynamic variables to be replaced
     * @param model  the model map containing variables of the prompt to replace those variables with values of this map.
     * @return the resolved prompt
     * @throws IllegalArgumentException if the prompt is null or empty, or if the model map is null
     */
    public static String getResolvedPrompt(String prompt, Map<String, Object> model) {
        Assert.hasText(prompt, "prompt must be non-null and non-empty");
        Assert.notNull(model, "model map must be non-null");
        PromptTemplate promptTemplate = PromptTemplate.builder()
                .template(prompt)
                .variables(model)
                .build();
        return promptTemplate.render();
    }

    /**
     * Retrieves the set of input variables from the system message of a given prompt model.
     *
     * @param promptModel the prompt model containing messages
     * @return a set of input variable names
     * @throws IllegalStateException if no system message is found in the prompt model
     * @throws IllegalArgumentException if the message list is empty
     */
    public static Set<String> getVariablesOfPromptX101(PromptModel promptModel) {
        List<MessageDto> messageDtos = promptModel.getMessages();
        Assert.notEmpty(messageDtos, "no message is found for promptId " + promptModel.getPromptId());

        MessageDto systemMessageDto = messageDtos.stream().filter(messageDto -> messageDto.getRole().equals("system")).findFirst().orElseThrow(() -> new IllegalStateException("system prompt does not exist"));
        PromptTemplateX101 promptTemplate = new PromptTemplateX101(systemMessageDto.getContent());
        return promptTemplate.getInputVariables();
    }

}
