package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.agent.AgentPrompt;
import com.enttribe.commons.ai.model.prompt.MessageDto;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * Utility class providing helper methods for AI agent-related operations.
 * This class contains static methods for handling system messages, prompt resolution,
 * and chat options configuration for AI agents.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class AgentUtils {

    /**
     * Private constructor to prevent instantiation of utility class.
     *
     * @throws UnsupportedOperationException always, as this utility class should not be instantiated
     */
    public AgentUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Retrieves and resolves the system message from a PromptModel using provided variable mappings.
     *
     * @param promptModel the prompt model containing message definitions
     * @param variableMap a map of variables to be resolved in the prompt template
     * @return resolved system message string
     * @throws IllegalStateException    if no system prompt exists in the messages
     * @throws IllegalArgumentException if the messages list is empty
     */
    public static String getSystemMessage(PromptModel promptModel, Map<String, Object> variableMap) {
        List<MessageDto> messageDtos = promptModel.getMessages();
        Assert.notEmpty(messageDtos, "no message is found for promptId " + promptModel.getPromptId());

        MessageDto systemMessageDto = messageDtos.stream().filter(messageDto -> messageDto.getRole().equals("system")).findFirst().orElseThrow(() -> new IllegalStateException("system prompt does not exist"));
        String content = systemMessageDto.getContent();
//        content += "<UUID> {UUID} </UUID>";
        return TemplateUtils.getResolvedPrompt(content, variableMap);
    }

    /**
     * Resolves variables in a system message using provided variable mappings.
     *
     * @param systemMessage the system message template to resolve
     * @param variableMap   a map of variables to be resolved in the system message
     * @return resolved system message string
     */
    public static String getSystemMessage(String systemMessage, Map<String, Object> variableMap) {
        return TemplateUtils.getResolvedPrompt(systemMessage, variableMap);
    }

    /**
     * Prepares chat options for an AI agent based on the provided agent prompt configuration.
     * Configures model, temperature, top-p sampling, and maximum tokens for the chat.
     *
     * @param agentPrompt the agent prompt configuration containing model parameters
     * @return configured ChatOptions instance
     */
    public static ChatOptions prepareChatOptions(AgentPrompt agentPrompt) {
        return OpenAiChatOptions.builder()
                .model(agentPrompt.getModel())
                .temperature(agentPrompt.getTemperature())
                .topP(agentPrompt.getTopP())
                .maxTokens(agentPrompt.getMaxTokens())
                .build();
    }

}
