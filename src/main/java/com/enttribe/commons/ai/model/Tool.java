package com.enttribe.commons.ai.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Tool {

    private String toolName;
    private Map<String, byte[]> byteCodeMap;
    private String className;
    private String toolId;
    private String requestType;
    private String description;
    private Boolean returnDirect;

    public Tool() {
    }

    public String getToolName() {
        return toolName;
    }

    public void setToolName(String toolName) {
        this.toolName = toolName;
    }

    public Map<String, byte[]> getByteCodeMap() {
        return byteCodeMap;
    }

    public void setByteCodeMap(Map<String, byte[]> byteCodeMap) {
        this.byteCodeMap = byteCodeMap;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getToolId() {
        return toolId;
    }

    public void setToolId(String toolId) {
        this.toolId = toolId;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getReturnDirect() {
        return returnDirect;
    }

    public void setReturnDirect(Boolean returnDirect) {
        this.returnDirect = returnDirect;
    }

}
