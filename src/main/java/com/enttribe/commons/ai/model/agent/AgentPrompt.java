package com.enttribe.commons.ai.model.agent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class AgentPrompt {

    private String promptId;
    private String systemMessage;
    private String provider = "groq";
    private String model = "llama-3.3-70b-versatile";
    private String conversationId = UUID.randomUUID().toString();
    private Double temperature = 0.4;
    private Double topP = 0.8;
    private Integer maxTokens;
    private List<String> knowledgeBaseIds = new ArrayList<>();
    private List<String> tools;
    private Map<String, Object> variableMap = new HashMap<>();
    private Map<String, Object> toolContext = new HashMap<>();

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private final AgentPrompt agentPrompt;

        private Builder() {
            agentPrompt = new AgentPrompt();
        }

        public Builder promptId(String promptId) {
            agentPrompt.promptId = promptId;
            return this;
        }

        public Builder systemMessage(String systemMessage) {
            agentPrompt.systemMessage = systemMessage;
            return this;
        }

        public Builder provider(String provider) {
            agentPrompt.provider = provider;
            return this;
        }

        public Builder model(String model) {
            agentPrompt.model = model;
            return this;
        }

        public Builder conversationId(String conversationId) {
            agentPrompt.conversationId = conversationId;
            return this;
        }

        public Builder temperature(Double temperature) {
            agentPrompt.temperature = temperature;
            return this;
        }

        public Builder topP(Double topP) {
            agentPrompt.topP = topP;
            return this;
        }

        public Builder maxTokens(Integer maxTokens) {
            agentPrompt.maxTokens = maxTokens;
            return this;
        }

        public Builder knowledgeBaseIds(List<String> knowledgeBaseIds) {
            agentPrompt.knowledgeBaseIds = knowledgeBaseIds;
            return this;
        }

        public Builder tools(List<String> tools) {
            agentPrompt.tools = tools;
            return this;
        }

        public Builder variableMap(Map<String, Object> variableMap) {
            agentPrompt.variableMap = variableMap;
            return this;
        }

        public Builder toolContext(Map<String, Object> toolContext) {
            agentPrompt.toolContext = toolContext;
            return this;
        }

        public AgentPrompt build() {
            return agentPrompt;
        }

    }

    public String getPromptId() {
        return promptId;
    }

    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    public String getSystemMessage() {
        return systemMessage;
    }

    public void setSystemMessage(String systemMessage) {
        this.systemMessage = systemMessage;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Double getTopP() {
        return topP;
    }

    public void setTopP(Double topP) {
        this.topP = topP;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public List<String> getKnowledgeBaseIds() {
        return knowledgeBaseIds;
    }

    public void setKnowledgeBaseIds(List<String> knowledgeBaseIds) {
        this.knowledgeBaseIds = knowledgeBaseIds;
    }

    public List<String> getTools() {
        return tools;
    }

    public void setTools(List<String> tools) {
        this.tools = tools;
    }

    public Map<String, Object> getVariableMap() {
        return variableMap;
    }

    public void setVariableMap(Map<String, Object> variableMap) {
        this.variableMap = variableMap;
    }

    public Map<String, Object> getToolContext() {
        return toolContext;
    }

    public void setToolContext(Map<String, Object> toolContext) {
        this.toolContext = toolContext;
    }

}
