package com.enttribe.commons.ai.model.rag;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class VectorMetaData {

    private static final Logger log = LoggerFactory.getLogger(VectorMetaData.class);
    private String vectorDatabase = "redis";
    private String databaseName = "default";
    private String collectionName = "vector-store";
    private String embeddingModel = "nomic-embed-text-v1_5-preview1";
    private String chatModel = "llama-3.3-70b-versatile";
    private String provider = "groq";
    private String indexName = "vector_store_knowledge_base";
    private String prefix = "prompt_smith_";

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public String toString() {
        return "VectorMetaData{" +
                "vectorDatabase='" + vectorDatabase + '\'' +
                ", databaseName='" + databaseName + '\'' +
                ", collectionName='" + collectionName + '\'' +
                ", embeddingModel='" + embeddingModel + '\'' +
                ", chatModel='" + chatModel + '\'' +
                ", provider='" + provider + '\'' +
                ", indexName='" + indexName + '\'' +
                ", prefix='" + prefix + '\'' +
                '}';
    }

    public static VectorMetaData fromMap(Map<String, String> valueMap) {
        VectorMetaData metaData = VectorMetaData.builder()
                .vectorDatabase(valueMap.get("vectorDatabase") != null ? valueMap.get("vectorDatabase") : "milvus")
                .provider(valueMap.get("provider") != null ? valueMap.get("provider") : "huggingface")
                .embeddingModel(valueMap.get("embeddingModel") != null ? valueMap.get("embeddingModel") : "BAAI/bge-base-en-v1.5")
                .chatModel(valueMap.get("chatModel") != null ? valueMap.get("chatModel") : "llama-3.3-70b-versatile")
                .databaseName(valueMap.get("databaseName") != null ? valueMap.get("databaseName") : "default")
                .collectionName(valueMap.get("collectionName") != null ? valueMap.get("collectionName") : "vector_store_knowledge_base")
                .indexName(valueMap.get("indexName") != null ? valueMap.get("indexName") : "emp_base")
                .prefix(valueMap.get("prefix") != null ? valueMap.get("prefix") : "emp_base")
                .build();

        log.debug("vector meta data from map : {}", metaData);
        return metaData;
    }

    public static class Builder {
        private final VectorMetaData vectorMetaData;

        private Builder() {
            this.vectorMetaData = new VectorMetaData();
        }

        public Builder vectorDatabase(String vectorDatabase) {
            vectorMetaData.vectorDatabase = vectorDatabase;
            return this;
        }

        public Builder databaseName(String databaseName) {
            vectorMetaData.databaseName = databaseName;
            return this;
        }

        public Builder collectionName(String collectionName) {
            vectorMetaData.collectionName = collectionName;
            return this;
        }

        public Builder embeddingModel(String embeddingModel) {
            vectorMetaData.embeddingModel = embeddingModel;
            return this;
        }

        public Builder chatModel(String chatModel) {
            vectorMetaData.chatModel = chatModel;
            return this;
        }

        public Builder provider(String provider) {
            vectorMetaData.provider = provider;
            return this;
        }

        public Builder indexName(String indexName) {
            vectorMetaData.indexName = indexName;
            return this;
        }

        public Builder prefix(String prefix) {
            vectorMetaData.prefix = prefix;
            return this;
        }

        public VectorMetaData build() {
            return vectorMetaData;
        }

    }

    public String getVectorDatabase() {
        return vectorDatabase;
    }

    public void setVectorDatabase(String vectorDatabase) {
        this.vectorDatabase = vectorDatabase;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getCollectionName() {
        return collectionName;
    }

    public void setCollectionName(String collectionName) {
        this.collectionName = collectionName;
    }

    public String getEmbeddingModel() {
        return embeddingModel;
    }

    public void setEmbeddingModel(String embeddingModel) {
        this.embeddingModel = embeddingModel;
    }

    public String getChatModel() {
        return chatModel;
    }

    public void setChatModel(String chatModel) {
        this.chatModel = chatModel;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }
}
