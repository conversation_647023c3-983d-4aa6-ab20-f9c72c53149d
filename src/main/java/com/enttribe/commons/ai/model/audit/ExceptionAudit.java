package com.enttribe.commons.ai.model.audit;


import java.util.Map;

/**
 * ExceptionAudit model to capture information for auditing.
 * <AUTHOR>
 */

public class ExceptionAudit {

    private Map<String, Object> identifier;
    private String applicationName;
    private String promptId;
    private String auditId;
    private String category;
    private String exceptionMessage;
    private String exceptionTrace;
    private String methodName;
    private Map<String, Object> methodParameters;

    private ExceptionAudit() {
    }


    public Map<String, Object> getIdentifier() {
        return identifier;
    }

    public String getCategory() {
        return category;
    }

    public String getPromptId() {
        return promptId;
    }

    public String getAuditId() {
        return auditId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public String getExceptionTrace() {
        return exceptionTrace;
    }

    public String getMethodName() {
        return methodName;
    }

    public Map<String, Object> getMethodParameters() {
        return methodParameters;
    }

    // Static builder() method to return a new Builder instance
    public static Builder builder() {
        return new Builder();
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    // Static nested Builder class
    public static class Builder {
        private final ExceptionAudit exceptionAudit;

        // Constructor initializes the ExceptionAudit instance
        private Builder() {
            exceptionAudit = new ExceptionAudit();
        }

        public Builder identifier(Map<String, Object> identifier) {
            exceptionAudit.identifier = identifier;
            return this;
        }

        public Builder applicationName(String applicationName) {
            exceptionAudit.applicationName = applicationName;
            return this;
        }

        public Builder promptId(String promptId) {
            exceptionAudit.promptId = promptId;
            return this;
        }

        public Builder auditId(String auditId) {
            exceptionAudit.auditId = auditId;
            return this;
        }

        public Builder category(String category) {
            exceptionAudit.category = category;
            return this;
        }

        public Builder exceptionMessage(String exceptionMessage) {
            exceptionAudit.exceptionMessage = exceptionMessage;
            return this;
        }

        public Builder exceptionTrace(String exceptionTrace) {
            exceptionAudit.exceptionTrace = exceptionTrace;
            return this;
        }

        public Builder methodName(String methodName) {
            exceptionAudit.methodName = methodName;
            return this;
        }

        public Builder methodParameters(Map<String, Object> methodParameters) {
            exceptionAudit.methodParameters = methodParameters;
            return this;
        }

        // Build method returns the constructed ExceptionAudit instance
        public ExceptionAudit build() {
            return exceptionAudit;
        }
    }
}

