package com.enttribe.commons.ai.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class KnowledgeBase {

    private String name;
    private String description;
    private String docMetaData;
    private String vectorMetaData;
    private Integer topK;
    private Double similarityThreshold;
    private String filter;
    private String type;
    private Boolean returnDirect;

    public KnowledgeBase() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDocMetaData() {
        return docMetaData;
    }

    public void setDocMetaData(String docMetaData) {
        this.docMetaData = docMetaData;
    }

    public String getVectorMetaData() {
        return vectorMetaData;
    }

    public void setVectorMetaData(String vectorMetaData) {
        this.vectorMetaData = vectorMetaData;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public Double getSimilarityThreshold() {
        return similarityThreshold;
    }

    public void setSimilarityThreshold(Double similarityThreshold) {
        this.similarityThreshold = similarityThreshold;
    }


    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getReturnDirect() {
        return returnDirect;
    }

    public void setReturnDirect(Boolean returnDirect) {
        this.returnDirect = returnDirect;
    }

}
