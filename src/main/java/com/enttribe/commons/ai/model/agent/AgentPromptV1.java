package com.enttribe.commons.ai.model.agent;

import org.springframework.ai.tool.ToolCallback;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * The type Agent prompt v 1.
 */
public class AgentPromptV1 {

    private String promptId;
    private String agentName;
    private String provider = "groq";
    private String model;
    private Double temperature = 0.4;
    private Double topP = 0.8;
    private Integer maxTokens;
    private List<String> knowledgeBaseIds = new ArrayList<>();
    private List<ToolCallback> mcpToolCallbacks = new ArrayList<>();
    private Map<String, Object> variableMap = new HashMap<>();
    private Map<String, Object> toolContext = new HashMap<>();
    private String conversationId = UUID.randomUUID().toString();
    private List<String> toolIds = new ArrayList<>();
    private Boolean shouldEnhanceIntent = false;

    /**
     * Creates a new builder for AgentPromptV1.
     *
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for AgentPromptV1.
     */
    public static class Builder {

        private final AgentPromptV1 agentPromptV1;

        private Builder() {
            agentPromptV1 = new AgentPromptV1();
        }

        /**
         * Prompt id builder.
         *
         * @param promptId the prompt id
         * @return the builder
         */
        public Builder promptId(String promptId) {
            agentPromptV1.promptId = promptId;
            return this;
        }

        /**
         * Agent name builder.
         *
         * @param agentName the agent name
         * @return the builder
         */
        public Builder agentName(String agentName) {
            agentPromptV1.agentName = agentName;
            return this;
        }

        /**
         * Provider builder.
         *
         * @param provider the provider
         * @return the builder
         */
        public Builder provider(String provider) {
            agentPromptV1.provider = provider;
            return this;
        }

        /**
         * Model builder.
         *
         * @param model the model
         * @return the builder
         */
        public Builder model(String model) {
            agentPromptV1.model = model;
            return this;
        }

        /**
         * Temperature builder.
         *
         * @param temperature the temperature
         * @return the builder
         */
        public Builder temperature(Double temperature) {
            agentPromptV1.temperature = temperature;
            return this;
        }

        /**
         * Top p builder.
         *
         * @param topP the top p
         * @return the builder
         */
        public Builder topP(Double topP) {
            agentPromptV1.topP = topP;
            return this;
        }

        /**
         * Max tokens builder.
         *
         * @param maxTokens the max tokens
         * @return the builder
         */
        public Builder maxTokens(Integer maxTokens) {
            agentPromptV1.maxTokens = maxTokens;
            return this;
        }

        /**
         * Knowledge base ids builder.
         *
         * @param knowledgeBaseIds the knowledge base ids
         * @return the builder
         */
        public Builder knowledgeBaseIds(List<String> knowledgeBaseIds) {
            agentPromptV1.knowledgeBaseIds = knowledgeBaseIds;
            return this;
        }

        /**
         * Mcp tool callbacks builder.
         *
         * @param mcpToolCallbacks the mcp tool callbacks
         * @return the builder
         */
        public Builder mcpToolCallbacks(List<ToolCallback> mcpToolCallbacks) {
            agentPromptV1.mcpToolCallbacks = mcpToolCallbacks;
            return this;
        }

        /**
         * Variable map builder.
         *
         * @param variableMap the variable map
         * @return the builder
         */
        public Builder variableMap(Map<String, Object> variableMap) {
            agentPromptV1.variableMap = variableMap;
            return this;
        }

        /**
         * Tool context builder.
         *
         * @param toolContext the tool context
         * @return the builder
         */
        public Builder toolContext(Map<String, Object> toolContext) {
            agentPromptV1.toolContext = toolContext;
            return this;
        }

        /**
         * Conversation id builder.
         *
         * @param conversationId the conversation id
         * @return the builder
         */
        public Builder conversationId(String conversationId) {
            agentPromptV1.conversationId = conversationId;
            return this;
        }

        /**
         * Tool ids builder.
         *
         * @param toolIds the tool ids
         * @return the builder
         */
        public Builder toolIds(List<String> toolIds) {
            agentPromptV1.toolIds = toolIds;
            return this;
        }

        /**
         * Should enhance intent builder.
         *
         * @param shouldEnhanceIntent the should enhance intent
         * @return the builder
         */
        public Builder shouldEnhanceIntent(Boolean shouldEnhanceIntent) {
            agentPromptV1.shouldEnhanceIntent = shouldEnhanceIntent;
            return this;
        }

        /**
         * Build agent prompt v 1.
         *
         * @return the agent prompt v 1
         */
        public AgentPromptV1 build() {
            return agentPromptV1;
        }
    }


    /**
     * Gets agent name.
     *
     * @return the agent name
     */
    public String getAgentName() {
        return agentName;
    }

    /**
     * Sets agent name.
     *
     * @param agentName the agent name
     */
    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    /**
     * Gets the temperature value.
     *
     * @return the temperature value
     */
    public Double getTemperature() {
        return temperature;
    }

    /**
     * Sets the temperature value.
     *
     * @param temperature the temperature value to set
     */
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    /**
     * Gets the topP value.
     *
     * @return the topP value
     */
    public Double getTopP() {
        return topP;
    }

    /**
     * Gets the provider.
     *
     * @return the provider
     */
    public String getProvider() {
        return provider;
    }

    /**
     * Sets the provider.
     *
     * @param provider the provider to set
     */
    public void setProvider(String provider) {
        this.provider = provider;
    }

    /**
     * Gets the model.
     *
     * @return the model
     */
    public String getModel() {
        return model;
    }

    /**
     * Sets the model.
     *
     * @param model the model to set
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * Sets the topP value.
     *
     * @param topP the topP value to set
     */
    public void setTopP(Double topP) {
        this.topP = topP;
    }

    /**
     * Gets the maximum tokens value.
     *
     * @return the maximum tokens value
     */
    public Integer getMaxTokens() {
        return maxTokens;
    }

    /**
     * Sets the maximum tokens value.
     *
     * @param maxTokens the maximum tokens value to set
     */
    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    /**
     * Gets the knowledge base IDs.
     *
     * @return the list of knowledge base IDs
     */
    public List<String> getKnowledgeBaseIds() {
        return knowledgeBaseIds;
    }

    /**
     * Sets the knowledge base IDs.
     *
     * @param knowledgeBaseIds the list of knowledge base IDs to set
     */
    public void setKnowledgeBaseIds(List<String> knowledgeBaseIds) {
        this.knowledgeBaseIds = knowledgeBaseIds;
    }

    /**
     * Gets mcp tool callbacks.
     *
     * @return the mcp tool callbacks
     */
    public List<ToolCallback> getMcpToolCallbacks() {
        return mcpToolCallbacks;
    }

    /**
     * Sets mcp tool callbacks.
     *
     * @param mcpToolCallbacks the mcp tool callbacks
     */
    public void setMcpToolCallbacks(List<ToolCallback> mcpToolCallbacks) {
        this.mcpToolCallbacks = mcpToolCallbacks;
    }

    /**
     * Gets the variable map.
     *
     * @return the variable map
     */
    public Map<String, Object> getVariableMap() {
        return variableMap;
    }

    /**
     * Sets the variable map.
     *
     * @param variableMap the variable map to set
     */
    public void setVariableMap(Map<String, Object> variableMap) {
        this.variableMap = variableMap;
    }

    /**
     * Gets the tool context map.
     *
     * @return the tool context map
     */
    public Map<String, Object> getToolContext() {
        return toolContext;
    }

    /**
     * Sets the tool context map.
     *
     * @param toolContext the tool context map to set
     */
    public void setToolContext(Map<String, Object> toolContext) {
        this.toolContext = toolContext;
    }

    /**
     * Gets the conversation ID.
     *
     * @return the conversation ID
     */
    public String getConversationId() {
        return conversationId;
    }

    /**
     * Sets the conversation ID.
     *
     * @param conversationId the conversation ID to set
     */
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    /**
     * Gets the prompt ID.
     *
     * @return the prompt ID
     */
    public String getPromptId() {
        return promptId;
    }

    /**
     * Sets the prompt ID.
     *
     * @param promptId the prompt ID to set
     */
    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    /**
     * Gets the tool IDs.
     *
     * @return the list of tool IDs
     */
    public List<String> getToolIds() {
        return toolIds;
    }

    /**
     * Sets the tool IDs.
     *
     * @param toolIds the list of tool IDs to set
     */
    public void setToolIds(List<String> toolIds) {
        this.toolIds = toolIds;
    }

    /**
     * Gets should enhance intent.
     *
     * @return the should enhance intent
     */
    public Boolean getShouldEnhanceIntent() {
        return shouldEnhanceIntent;
    }

    /**
     * Sets should enhance intent.
     *
     * @param shouldEnhanceIntent the should enhance intent
     */
    public void setShouldEnhanceIntent(Boolean shouldEnhanceIntent) {
        this.shouldEnhanceIntent = shouldEnhanceIntent;
    }

}