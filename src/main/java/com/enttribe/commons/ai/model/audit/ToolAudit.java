package com.enttribe.commons.ai.model.audit;

import java.util.Date;

public class ToolAudit {

    private String auditId;
    private String agentName;
    private String promptId;
    private String promptName;
    private String requestText;
    private String toolCallRequest;
    private String toolDefinitions;
    private String chatOptions;
    private String toolResponse;
    private Integer totalToken;
    private Integer promptToken;
    private Integer generationTokens;
    private Long responseTime;
    private Date creationTime;
    private Date startTime;
    private Date endTime;
    private String model;
    private String provider;
    private String status;
    private String applicationName;

    public ToolAudit() {
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final ToolAudit toolAudit;

        private Builder() {
            toolAudit = new ToolAudit();
        }

        public ToolAudit.Builder requestText(String requestText) {
            toolAudit.requestText = requestText;
            return this;
        }

        public ToolAudit.Builder chatOptions(String chatOptions) {
            toolAudit.chatOptions = chatOptions;
            return this;
        }

        public ToolAudit.Builder auditId(String auditId) {
            toolAudit.auditId = auditId;
            return this;
        }

        public ToolAudit.Builder toolCallRequest(String toolCallRequest) {
            toolAudit.toolCallRequest = toolCallRequest;
            return this;
        }

        public ToolAudit.Builder toolDefinitions(String toolDefinitions) {
            toolAudit.toolDefinitions = toolDefinitions;
            return this;
        }

        public ToolAudit.Builder toolResponse(String toolResponse) {
            toolAudit.toolResponse = toolResponse;
            return this;
        }

        public ToolAudit.Builder agentName(String agentName) {
            toolAudit.agentName = agentName;
            return this;
        }

        public ToolAudit.Builder promptId(String promptId) {
            toolAudit.promptId = promptId;
            return this;
        }

        public ToolAudit.Builder promptName(String promptName) {
            toolAudit.promptName = promptName;
            return this;
        }

        public ToolAudit.Builder totalToken(Integer totalToken) {
            toolAudit.totalToken = totalToken;
            return this;
        }

        public ToolAudit.Builder promptToken(Integer promptToken) {
            toolAudit.promptToken = promptToken;
            return this;
        }

        public ToolAudit.Builder generationTokens(Integer generationTokens) {
            toolAudit.generationTokens = generationTokens;
            return this;
        }

        public ToolAudit.Builder responseTime(Long responseTime) {
            toolAudit.responseTime = responseTime;
            return this;
        }

        public ToolAudit.Builder creationTime(Date creationTime) {
            toolAudit.creationTime = creationTime;
            return this;
        }

        public ToolAudit.Builder startTime(Date startTime) {
            toolAudit.startTime = startTime;
            return this;
        }

        public ToolAudit.Builder endTime(Date endTime) {
            toolAudit.endTime = endTime;
            return this;
        }

        public ToolAudit.Builder model(String model) {
            toolAudit.model = model;
            return this;
        }

        public ToolAudit.Builder provider(String provider) {
            toolAudit.provider = provider;
            return this;
        }

        public ToolAudit.Builder status(String status) {
            toolAudit.status = status;
            return this;
        }

        public ToolAudit.Builder applicationName(String applicationName) {
            toolAudit.applicationName = applicationName;
            return this;
        }

        public ToolAudit build() {
            return toolAudit;
        }

    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRequestText() {
        return requestText;
    }

    public void setRequestText(String requestText) {
        this.requestText = requestText;
    }

    public String getChatOptions() {
        return chatOptions;
    }

    public void setChatOptions(String chatOptions) {
        this.chatOptions = chatOptions;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getToolCallRequest() {
        return toolCallRequest;
    }

    public void setToolCallRequest(String toolCallRequest) {
        this.toolCallRequest = toolCallRequest;
    }

    public String getToolDefinitions() {
        return toolDefinitions;
    }

    public void setToolDefinitions(String toolDefinitions) {
        this.toolDefinitions = toolDefinitions;
    }

    public String getToolResponse() {
        return toolResponse;
    }

    public void setToolResponse(String toolResponse) {
        this.toolResponse = toolResponse;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getPromptId() {
        return promptId;
    }

    public void setPromptId(String promptId) {
        this.promptId = promptId;
    }

    public Integer getTotalToken() {
        return totalToken;
    }

    public void setTotalToken(Integer totalToken) {
        this.totalToken = totalToken;
    }

    public Integer getPromptToken() {
        return promptToken;
    }

    public void setPromptToken(Integer promptToken) {
        this.promptToken = promptToken;
    }

    public Integer getGenerationTokens() {
        return generationTokens;
    }

    public void setGenerationTokens(Integer generationTokens) {
        this.generationTokens = generationTokens;
    }

    public String getPromptName() {
        return promptName;
    }

    public void setPromptName(String promptName) {
        this.promptName = promptName;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
