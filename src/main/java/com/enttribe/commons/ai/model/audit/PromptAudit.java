package com.enttribe.commons.ai.model.audit;

import java.util.Date;
import java.util.Map;

public class PromptAudit {

    private String applicationName;
    private String agentName;
    private Map<String, String> metaTags;
    private Map<String, Object> chatOptions;
    private String functionArgs;  //This is for Agentic audit
    private String auditId;
    private String promptId;
    private String model;
    private String responseText;
    private Integer totalToken;
    private Integer promptToken;
    private Integer generationTokens;
    private Long responseTime;
    private String promptName;
    private Date creationTime;
    private Date startTime;
    private Date endTime;
    private Integer httpStatus;
    private String requestText;
    private String status;
    private String errorMessage;
    private String provider;
    private String toolCallDefinitions;

    private PromptAudit() {
    }

    public Map<String, String> getMetaTags() {
        return metaTags;
    }

    public Map<String, Object> getChatOptions() {
        return chatOptions;
    }

    public String getFunctionArgs() {
        return functionArgs;
    }

    public String getAuditId() {
        return auditId;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public String getAgentName() {
        return agentName;
    }

    public String getPromptId() {
        return promptId;
    }

    public String getModel() {
        return model;
    }

    public String getResponseText() {
        return responseText;
    }

    public Integer getTotalToken() {
        return totalToken;
    }

    public Integer getPromptToken() {
        return promptToken;
    }

    public Integer getGenerationTokens() {
        return generationTokens;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public String getPromptName() {
        return promptName;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public Integer getHttpStatus() {
        return httpStatus;
    }

    public String getRequestText() {
        return requestText;
    }

    public String getStatus() {
        return status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public String getProvider() {
        return provider;
    }

    public String getToolCallDefinitions() {
        return toolCallDefinitions;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final PromptAudit promptAudit;

        private Builder() {
            promptAudit = new PromptAudit();
        }

        public Builder metaTags(Map<String, String> metaTags) {
            promptAudit.metaTags = metaTags;
            return this;
        }

        public Builder chatOptions(Map<String, Object> chatOptions) {
            promptAudit.chatOptions = chatOptions;
            return this;
        }

        public Builder functionArgs(String functionArgs) {
            promptAudit.functionArgs = functionArgs;
            return this;
        }

        public Builder auditId(String auditId) {
            promptAudit.auditId = auditId;
            return this;
        }

        public Builder applicationName(String applicationName) {
            promptAudit.applicationName = applicationName;
            return this;
        }

        public Builder agentName(String agentName) {
            promptAudit.agentName = agentName;
            return this;
        }

        public Builder promptId(String promptId) {
            promptAudit.promptId = promptId;
            return this;
        }

        public Builder model(String model) {
            promptAudit.model = model;
            return this;
        }

        public Builder responseText(String responseText) {
            promptAudit.responseText = responseText;
            return this;
        }

        public Builder totalToken(Integer totalToken) {
            promptAudit.totalToken = totalToken;
            return this;
        }

        public Builder promptToken(Integer promptToken) {
            promptAudit.promptToken = promptToken;
            return this;
        }

        public Builder generationTokens(Integer generationTokens) {
            promptAudit.generationTokens = generationTokens;
            return this;
        }

        public Builder responseTime(Long responseTime) {
            promptAudit.responseTime = responseTime;
            return this;
        }

        public Builder promptName(String promptName) {
            promptAudit.promptName = promptName;
            return this;
        }

        public Builder requestText(String requestText) {
            promptAudit.requestText = requestText;
            return this;
        }

        public Builder status(String status) {
            promptAudit.status = status;
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            promptAudit.errorMessage = errorMessage;
            return this;
        }

        public Builder provider(String provider) {
            promptAudit.provider = provider;
            return this;
        }

        public Builder toolCallDefinitions(String toolCallDefinitions) {
            promptAudit.toolCallDefinitions = toolCallDefinitions;
            return this;
        }

        public Builder creationTime(Date creationTime) {
            promptAudit.creationTime = creationTime;
            return this;
        }

        public Builder startTime(Date startTime) {
            promptAudit.startTime = startTime;
            return this;
        }

        public Builder endTime(Date endTime) {
            promptAudit.endTime = endTime;
            return this;
        }

        public Builder httpStatus(Integer httpStatus) {
            promptAudit.httpStatus = httpStatus;
            return this;
        }

        public PromptAudit build() {
            return promptAudit;
        }
    }
}
