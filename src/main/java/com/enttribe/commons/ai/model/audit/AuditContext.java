package com.enttribe.commons.ai.model.audit;

import com.enttribe.commons.ai.model.PromptModel;

public class AuditContext {

    private PromptModel promptModel;
    private String auditId;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private final AuditContext auditContext;

        public Builder() {
            this.auditContext = new AuditContext();
        }

        public Builder promptModel(PromptModel promptModel) {
            auditContext.promptModel = promptModel;
            return this;
        }

        public Builder auditId(String auditId) {
            auditContext.auditId = auditId;
            return this;
        }

        public AuditContext build() {
            return auditContext;
        }

    }

    public PromptModel getPromptModel() {
        return promptModel;
    }

    public void setPromptModel(PromptModel promptModel) {
        this.promptModel = promptModel;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

}
