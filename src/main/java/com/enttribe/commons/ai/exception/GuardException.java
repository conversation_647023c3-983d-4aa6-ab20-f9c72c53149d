package com.enttribe.commons.ai.exception;

public class GuardException extends RuntimeException {

    public GuardException() {
    }

    public GuardException(String message) {
        super(message);
    }

    public GuardException(String message, Throwable cause) {
        super(message, cause);
    }

    public GuardException(Throwable cause) {
        super(cause);
    }

    public GuardException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
