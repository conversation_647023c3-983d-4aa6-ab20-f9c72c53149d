package com.enttribe.commons.ai.exception;

import java.util.Map;

/**
 * Audit exception.
 *
 * <AUTHOR>
 */
public class ErrorAuditException extends RuntimeException {

    private Map<String, Object> identifier;
    private String applicationName;
    private String promptId;

    public Map<String, Object> getIdentifier() {
        return identifier;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public String getPromptId() {
        return promptId;
    }


    /**
     * Instantiates a new Audit exception.
     */
    public ErrorAuditException() {
    }

    /**
     * Instantiates a new Audit exception.
     *
     * @param message the message
     */
    public ErrorAuditException(String message) {
        super(message);
    }


    /**
     * Instantiates a new Audit exception.
     *
     * @param message the message
     * @param cause   the cause
     */
    public ErrorAuditException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Instantiates a new Audit exception and initialize identifier.
     *
     * @param message    the message
     * @param cause      the cause
     * @param identifier the identifier
     */
    public ErrorAuditException(String message, Throwable cause, Map<String, Object> identifier) {
        super(message, cause);
        this.identifier = identifier;
    }


    /**
     * Instantiates a new Error audit exception.
     *
     * @param message         the message
     * @param cause           the cause
     * @param applicationName the application name
     * @param promptId        the prompt id
     */
    public ErrorAuditException(String message, Throwable cause, String applicationName, String promptId) {
        super(message, cause);
        this.applicationName = applicationName;
        this.promptId = promptId;
    }

}
