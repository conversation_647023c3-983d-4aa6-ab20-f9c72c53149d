package com.enttribe.commons.ai.function.registrar;

import com.enttribe.commons.ai.function.loader.DynamicClassLoader;
import com.enttribe.commons.ai.model.Tool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * A registrar for dynamically managing Spring beans at runtime.
 * This class provides functionality to register, re-register, and unregister beans
 * in the Spring application context dynamically using bytecode.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DynamicBeanRegistrar {

    private static final Logger log = LoggerFactory.getLogger(DynamicBeanRegistrar.class);
    private final ApplicationContext applicationContext;

    /**
     * Constructs a new DynamicBeanRegistrar with the specified ApplicationContext.
     *
     * @param applicationContext the Spring ApplicationContext to use for bean registration
     */
    public DynamicBeanRegistrar(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * Registers a new bean in the Spring context using the provided bytecode.
     * This method will fail if a bean with the same name already exists.
     *
     * @param beanName  the name to register the bean under
     * @param className the fully qualified class name of the bean
     * @param byteMap   a map containing the bytecode for the class and its dependencies
     * @throws ClassNotFoundException if the class cannot be loaded from the provided bytecode
     */
    public void registerBean(String beanName, String className, Map<String, byte[]> byteMap) throws ClassNotFoundException {

        // Load the class dynamically
        DynamicClassLoader classLoader = new DynamicClassLoader(byteMap);
        Class<?> clazz = classLoader.loadClass(className);
        Class<?> req = classLoader.loadClass(className + "$Request");

        // Register the bean
        DefaultListableBeanFactory beanFactory =
                (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
        beanFactory.registerBeanDefinition(beanName, beanDefinitionBuilder.getBeanDefinition());
    }

    /**
     * Registers or re-registers a bean in the Spring context.
     * If a bean with the same name exists, it will be unregistered first.
     *
     * @param beanName  the name to register the bean under
     * @param className the fully qualified class name of the bean
     * @param byteMap   a map containing the bytecode for the class and its dependencies
     * @throws ClassNotFoundException if the class cannot be loaded from the provided bytecode
     */
    public void registerNewBean(String beanName, String className, Map<String, byte[]> byteMap) throws ClassNotFoundException {

        boolean containsBean = applicationContext.containsBean(beanName);
        if (containsBean) {
            log.debug("Bean already exists with name: {}. Unregistering existing bean...", beanName);
            unregisterBean(beanName);
        }
        // Load the class dynamically
        DynamicClassLoader classLoader = new DynamicClassLoader(byteMap);
        Class<?> clazz = classLoader.loadClass(className);
        Class<?> req = classLoader.loadClass(className + "$Request");

        // Register the bean
        DefaultListableBeanFactory beanFactory =
                (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
        beanFactory.registerBeanDefinition(beanName, beanDefinitionBuilder.getBeanDefinition());
        log.debug("successfully registered bean with name : {}", beanName);
    }

    public List<ToolCallback> registerToolCallbacks(List<Tool> tools) {
        List<ToolCallback> functionToolCallbacks = new ArrayList<>();
        for (Tool tool : tools) {
            try {
                String beanName = tool.getToolName();
                boolean containsBean = applicationContext.containsBean(beanName);
                if (containsBean) {
                    log.info("Bean already exists with name: {}. Unregistering existing bean...", beanName);
                    unregisterBean(beanName);
                }
                // Load the class dynamically
                DynamicClassLoader classLoader = new DynamicClassLoader(tool.getByteCodeMap());
                String className = tool.getClassName();
                Class<?> clazz = classLoader.loadClass(className);
                Class<?> req = classLoader.loadClass(className + "$Request");

                // Register the bean
                DefaultListableBeanFactory beanFactory =
                        (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
                BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
                beanFactory.registerBeanDefinition(beanName, beanDefinitionBuilder.getBeanDefinition());
                log.info("successfully registered bean with name : {}", beanName);

                Object object = applicationContext.getBean(beanName);
                beanFactory.autowireBean(object);
                boolean returnDirect = tool.getReturnDirect() != null && tool.getReturnDirect();

                if (object instanceof Function<?, ?>) {
                    log.info("bean : {} is a function", beanName);
                    Function function = (Function) object;
                    ToolCallback toolCallback = FunctionToolCallback.builder(tool.getToolName(), function)
                            .toolMetadata(ToolMetadata.builder().returnDirect(returnDirect).build())
                            .description(tool.getDescription())
                            .inputType(req)
                            .build();
                    functionToolCallbacks.add(toolCallback);
                } else if (object instanceof BiFunction<?, ?, ?>) {
                    log.info("bean : {} is a bi-function", beanName);
                    BiFunction biFunction = (BiFunction) object;
                    ToolCallback toolCallback = FunctionToolCallback.builder(tool.getToolName(), biFunction)
                            .toolMetadata(ToolMetadata.builder().returnDirect(returnDirect).build())
                            .description(tool.getDescription())
                            .inputType(req)
                            .build();
                    functionToolCallbacks.add(toolCallback);
                }
            } catch (Exception e) {
                log.error("error registering bean for tool : {}", tool.getToolId(), e);
            }

        }
        return functionToolCallbacks;
    }

    /**
     * Unregisters a bean from the Spring context.
     *
     * @param beanName the name of the bean to unregister
     */
    public void unregisterBean(String beanName) {
        DefaultListableBeanFactory beanFactory =
                (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        if (beanFactory.containsBeanDefinition(beanName)) {
            beanFactory.removeBeanDefinition(beanName);
            log.debug("Successfully unregistered bean: {}", beanName);
        } else {
            log.warn("Cannot unregister bean: {}. Bean definition not found.", beanName);
        }
    }

}