/**
 * A custom ClassLoader implementation that can load classes from in-memory byte arrays.
 * This class loader is particularly useful when classes need to be generated or loaded
 * dynamically at runtime without existing as physical files in the file system.
 *
 * <p>The loader maintains a map of class names to their corresponding byte array
 * representations and can load these classes on demand. If a requested class is not
 * found in the internal map, it delegates the loading to its parent class loader.</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
package com.enttribe.commons.ai.function.loader;

import java.util.Map;

public class DynamicClassLoader extends ClassLoader {

    /**
     * Map containing class names as keys and their corresponding byte array representations as values.
     */
    private final Map<String, byte[]> classBytes;

    /**
     * Constructs a new DynamicClassLoader with the specified class bytes map.
     *
     * @param classBytes A map containing class names as keys and their byte array representations as values
     */
    public DynamicClassLoader(Map<String, byte[]> classBytes) {
        this.classBytes = classBytes;
    }

    /**
     * Finds and defines a class given its name. This method is called by loadClass when
     * the class is not found in the parent class loader.
     *
     * @param name The fully qualified name of the class to find
     * @return The Class object representing the loaded class
     * @throws ClassNotFoundException if the class cannot be found in the internal map
     */
    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        byte[] bytes = classBytes.get(name);
        if (bytes == null) {
            throw new ClassNotFoundException(name);
        }
        return defineClass(name, bytes, 0, bytes.length);
    }

    /**
     * Loads a class with the specified name. First attempts to find the class in the
     * internal map of class bytes. If not found, delegates to the parent class loader.
     *
     * @param name    The fully qualified name of the class to load
     * @param resolve If true, resolve the class
     * @return The Class object representing the loaded class
     * @throws ClassNotFoundException if the class cannot be found
     */
    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        // First try to find the class in the classBytes map
        if (classBytes.containsKey(name)) {
            return findClass(name);
        }
        // Fallback to parent class loader
        return super.loadClass(name, resolve);
    }

}
