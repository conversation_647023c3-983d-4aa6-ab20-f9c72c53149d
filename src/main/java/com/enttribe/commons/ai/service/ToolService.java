package com.enttribe.commons.ai.service;

import com.enttribe.commons.ai.function.registrar.DynamicBeanRegistrar;
import com.enttribe.commons.ai.model.Tool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service class responsible for managing dynamic tool registration in the Spring application context.
 * This service provides functionality to register tools as Spring beans at runtime, enabling
 * dynamic extension of application capabilities.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ToolService {

    private static final Logger log = LoggerFactory.getLogger(ToolService.class);
    private final ApplicationContext applicationContext;
    private final DynamicBeanRegistrar dynamicBeanRegistrar;

    /**
     * Constructs a new ToolService with the provided ApplicationContext.
     *
     * @param applicationContext the Spring ApplicationContext used for bean registration
     */
    public ToolService(ApplicationContext applicationContext, DynamicBeanRegistrar dynamicBeanRegistrar) {
        this.applicationContext = applicationContext;
        this.dynamicBeanRegistrar = dynamicBeanRegistrar;
    }

    /**
     * Registers a list of tools as Spring beans in the application context.
     * Each tool is registered using its tool name as the bean name and the provided class information.
     * If registration fails for a tool, the error is logged and the process continues with the remaining tools.
     *
     * @param tools a list of Tool objects containing the necessary information for bean registration
     * @return a list of successfully registered tool names
     */
    public List<String> registerBeans(List<Tool> tools) {
        log.info("inside @method registerBeans");
        List<String> registeredTools = new ArrayList<>();

        for (Tool tool : tools) {
            DynamicBeanRegistrar dynamicBeanRegistrar = new DynamicBeanRegistrar(applicationContext);
            try {
                dynamicBeanRegistrar.registerNewBean(tool.getToolName(), tool.getClassName(), tool.getByteCodeMap());
                registeredTools.add(tool.getToolName());
            } catch (ClassNotFoundException e) {
                log.error("error registering bean for tool : {}", tool.getToolId(), e);
            }
        }

        return registeredTools;
    }

    public List<ToolCallback> registerToolCallback(List<Tool> tools) {
        log.info("inside @method registerToolCallback");
        return dynamicBeanRegistrar.registerToolCallbacks(tools);
    }

}
