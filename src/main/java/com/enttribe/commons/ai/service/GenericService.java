package com.enttribe.commons.ai.service;

import com.enttribe.commons.ai.advisor.AuditAdvisor;
import com.enttribe.commons.ai.advisor.LlmGuardAdvisor;
import com.enttribe.commons.ai.audit.AuditService;
import com.enttribe.commons.ai.config.AppProperties;
import com.enttribe.commons.ai.manager.AdvisorManager;
import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.manager.PromptManager;
import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.Tool;
import com.enttribe.commons.ai.model.audit.AuditContext;
import com.enttribe.commons.ai.model.audit.ExceptionAudit;
import com.enttribe.commons.ai.model.audit.PromptAudit;
import com.enttribe.commons.ai.util.AuditUtils;
import com.enttribe.commons.ai.util.ChatModelUtils;
import com.enttribe.commons.ai.util.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.metadata.Usage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * A generic service class that handles LLM (Large Language Model) interactions, response processing,
 * and audit logging. This service provides functionality for making LLM calls with various prompt configurations,
 * handling responses in different formats, and managing the audit trail of these interactions.
 * The service supports:
 * - Generic response handling with type conversion
 * - Prompt management and execution
 * - LLM interaction auditing
 * - JSON response rectification
 * - Chat session management
 * - Tool integration
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class GenericService {

    private static final String STATUS_SUCCESS = "success";
    private static final Logger log = LoggerFactory.getLogger(GenericService.class);

    @Value("classpath:rectify_json_system_prompt.st")
    private Resource rectifyJsonSystemPrompt;

    @Value("classpath:rectify_json_user_prompt.st")
    private Resource rectifyJsonUserPrompt;

    @Value("${commons.ai.sdk.app.name}")
    private String applicationName;

    @Value("${prompt.audit.enable:false}")
    private Boolean promptAuditEnabled;

    @Value("${commons.ai.sdk.chat-size:15}")
    private Integer chatSize;

    private static final String defaultModel = AppProperties.getProperty("commons.ai.sdk.default.llm.model", "llama-3.3-70b-versatile");
    private final Advisor llmGuardAdvisor;

    /**
     * Instantiates a new GenericService instance.
     *
     * @param promptManager    the prompt manager for handling prompt templates and configurations
     * @param auditService     the audit service for logging LLM interactions
     * @param inferenceManager the inference manager for LLM model management
     * @param advisorManager   the advisor manager for chat advisors
     */
    public GenericService(Advisor llmGuardAdvisor, PromptManager promptManager, AuditService auditService,
                          InferenceManager inferenceManager, AdvisorManager advisorManager,
                          AuditAdvisor auditAdvisor, PromptApi promptApi, ToolService toolService) {
        this.llmGuardAdvisor = llmGuardAdvisor;
        this.promptManager = promptManager;
        this.auditService = auditService;
        this.inferenceManager = inferenceManager;
        this.advisorManager = advisorManager;
        this.auditAdvisor = auditAdvisor;
        this.promptApi = promptApi;
        this.toolService = toolService;
    }

    private final AuditService auditService;
    private final PromptManager promptManager;
    private final InferenceManager inferenceManager;
    private final AdvisorManager advisorManager;
    private final AuditAdvisor auditAdvisor;
    private final PromptApi promptApi;
    private final ToolService toolService;

    /**
     * Processes a prompt and returns a typed response based on the specified format.
     *
     * @param <T>         the type parameter for the response
     * @param promptId    the unique identifier for the prompt to be used
     * @param tools       the set of tools to be made available for the prompt execution
     * @param variableMap the map of variables to be used in the prompt template
     * @param format      the class type to convert the response into
     * @param auditId     the audit identifier for tracking the request
     * @param chatId      the chat session identifier
     * @return the response converted to the specified type
     * @throws IllegalArgumentException if the chat response is null
     */
    public <T> T getGenericResponse(String promptId, Set<String> tools, Map<String, Object> variableMap, Class<T> format, String auditId, String chatId) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);
        if (!CollectionUtils.isEmpty(tools)) promptModel.setTools(tools);

        BeanOutputConverter<T> outputConverter = new BeanOutputConverter<>(format);
        Map<String, Object> mutableMap = prepareVariableMap(variableMap, outputConverter);

        String result = getLlmResponseAndDoPromptAudit(promptModel, mutableMap, auditId, chatId);
        if (format.isAssignableFrom(String.class)) return (T) result;
        return getTypedResponse(outputConverter, result, promptModel, auditId);
    }

    /**
     * Processes a prompt and returns a typed response using a ParameterizedTypeReference for complex types.
     *
     * @param <T>           the type parameter for the response
     * @param promptId      the unique identifier for the prompt to be used
     * @param tools         the set of tools to be made available for the prompt execution
     * @param variableMap   the map of variables to be used in the prompt template
     * @param typeReference the type reference for complex return types
     * @param auditId       the audit identifier for tracking the request
     * @param chatId        the chat session identifier
     * @return the response converted to the specified type
     * @throws IllegalArgumentException if the chat response is null
     */
    public <T> T getGenericResponse(String promptId, Set<String> tools, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference, String auditId, String chatId) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);
        if (!CollectionUtils.isEmpty(tools)) promptModel.setTools(tools);

        BeanOutputConverter<T> outputConverter = new BeanOutputConverter<>(typeReference);
        Map<String, Object> mutableMap = prepareVariableMap(variableMap, outputConverter);

        String result = getLlmResponseAndDoPromptAudit(promptModel, mutableMap, auditId, chatId);
        return getTypedResponse(outputConverter, result, promptModel, auditId);
    }

    /**
     * Gets LLM response and performs prompt auditing.
     *
     * @param promptModel the prompt model containing configuration
     * @param messages    the list of messages to be sent to the LLM
     * @param auditId     the audit identifier for tracking
     * @return the LLM response content as a string
     * @throws IllegalArgumentException if the chat response is null
     */
    public String getLlmResponseAndDoPromptAudit(PromptModel promptModel, List<Message> messages, String auditId) {

        ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel);

        AuditContext auditContext = AuditContext.builder()
                .promptModel(promptModel)
                .auditId(auditId)
                .build();

        ChatResponse chatResponse = inferenceManager.getChatClientByProvider(promptModel.getProvider())
                .prompt()
                .messages(messages)
                .options(chatOptions)
                .toolContext(LlmGuardAdvisor.getGuardContext(promptModel.getLlmGuard(), messages))
                .toolContext(Map.of("auditContext", auditContext))
                .call()
                .chatResponse();

        Assert.notNull(chatResponse, "chat response is null");
        return chatResponse.getResult().getOutput().getText();
    }

    public String getLlmResponseAndDoPromptAuditX101(PromptModel promptModel, List<String> toolIds, List<Message> messages, ChatOptions chatOptions, String auditId, boolean allowGuard) {

        if (chatOptions == null) {
            chatOptions = ChatModelUtils.prepareChatOptions(promptModel);
        }

        AuditContext auditContext = AuditContext.builder()
                .promptModel(promptModel)
                .auditId(auditId)
                .build();

        if (toolIds == null) toolIds = new ArrayList<>();

        List<Tool> tools = this.promptApi.getToolsByIds(toolIds);
        List<ToolCallback> registeredTools = this.toolService.registerToolCallback(tools);
        List<String> toolList = registeredTools.stream().map(toolCallback -> toolCallback.getToolDefinition().name()).toList();
        log.info("registered tools are : {}", toolList);

        ChatClient chatClient = inferenceManager.getChatClientByProvider(promptModel.getProvider(), allowGuard);

        ChatResponse chatResponse = chatClient
                .prompt()
                .messages(messages)
                .options(chatOptions)
                .toolCallbacks(registeredTools)
                .toolContext(LlmGuardAdvisor.getGuardContext(promptModel.getLlmGuard(), messages))
                .toolContext(Map.of("auditContext", auditContext))
                .call()
                .chatResponse();

        Assert.notNull(chatResponse, "chat response is null");
        return chatResponse.getResult().getOutput().getText();
    }

    /**
     * Prepares the variable map for prompt template rendering.
     *
     * @param <T>             the type parameter for the response
     * @param variableMap     the original variable map
     * @param outputConverter the output converter for type conversion
     * @return the prepared variable map
     */
    private <T> Map<String, Object> prepareVariableMap(Map<String, Object> variableMap, BeanOutputConverter<T> outputConverter) {
        Map<String, Object> mutableMap = new HashMap<>();
        if (variableMap != null) {
            mutableMap.putAll(variableMap);
        }
        mutableMap.put("format", outputConverter.getFormat());
        return mutableMap;
    }

    /**
     * Gets LLM response and performs prompt auditing with variable map support.
     *
     * @param promptModel the prompt model containing configuration
     * @param variableMap the map of variables for template rendering
     * @param auditId     the audit identifier for tracking
     * @param chatId      the chat session identifier
     * @return the LLM response content as a string
     * @throws IllegalArgumentException if the chat response is null
     */
    private String getLlmResponseAndDoPromptAudit(PromptModel promptModel, Map<String, Object> variableMap, String auditId, String chatId) {

        List<Message> messages = ChatModelUtils.getResolvedMessages(promptModel, variableMap);
        ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel);

        ChatResponse chatResponse = getChatResponse(promptModel, messages, chatOptions, chatId, auditId);
        Assert.notNull(chatResponse, "chat response is null");
        return chatResponse.getResult().getOutput().getText();
    }

    /**
     * Gets chat response from the LLM.
     *
     * @param promptModel the prompt model containing configuration
     * @param messages    the list of messages to be sent
     * @param chatOptions the chat options for the request
     * @param chatId      the chat session identifier
     * @param auditId     audit id associated with the request
     * @return the chat response from the LLM
     */
    private ChatResponse getChatResponse(PromptModel promptModel, List<Message> messages, ChatOptions chatOptions, String chatId, String auditId) {
        ChatClient chatClient = getChatClient(promptModel.getProvider(), chatId);

        AuditContext auditContext = AuditContext.builder()
                .promptModel(promptModel)
                .auditId(auditId)
                .build();

        return chatClient
                .prompt()
                .messages(messages)
                .toolContext(LlmGuardAdvisor.getGuardContext(promptModel.getLlmGuard(), messages))
                .toolContext(Map.of("auditContext", auditContext))
                .options(chatOptions)
                .call()
                .chatResponse();
    }

    /**
     * Gets the appropriate chat client based on provider and chat ID.
     *
     * @param provider the LLM provider identifier
     * @param chatId   the chat session identifier
     * @return configured chat client instance
     */
    private ChatClient getChatClient(String provider, String chatId) {
        ChatModel chatModel = inferenceManager.getChatModelByProvider(provider);
        if (chatId == null) {
            return ChatClient.builder(chatModel)
                    .defaultAdvisors(llmGuardAdvisor, auditAdvisor)
                    .build();
        } else {
            return ChatClient.builder(chatModel)
                    .defaultAdvisors(ChatModelUtils.getChatAdvisorSpec(chatId, chatSize))
                    .defaultAdvisors(advisorManager.getChatMemoryAdvisor(), llmGuardAdvisor, auditAdvisor)
                    .build();
        }
    }

    /**
     * Converts the LLM response to the specified type, with JSON rectification if needed.
     *
     * @param <T>             the type parameter for the response
     * @param outputConverter the converter for the output type
     * @param jsonString      the JSON string to convert
     * @param promptModel     the prompt model containing configuration
     * @param auditId         the audit identifier for tracking
     * @return the converted response
     * @throws IllegalArgumentException if conversion fails after rectification
     */
    public <T> T getTypedResponse(BeanOutputConverter<T> outputConverter, String jsonString, PromptModel promptModel, String auditId) {
        T convert;
        try {
            convert = outputConverter.convert(jsonString);
        } catch (Exception e) {
            log.warn("failed to parse JSON response, attempting to rectify", e);
            ExceptionAudit exceptionAudit = ExceptionUtils.getParseExceptionAudit(e, jsonString, outputConverter.getFormat(), promptModel.getProvider(), applicationName);
            auditService.sendExceptionAudit(exceptionAudit);
            //Write code to send to error audit.
            jsonString = rectifyJSON(jsonString, outputConverter.getFormat(), promptModel, auditId);
            convert = outputConverter.convert(jsonString);
        }
        return convert;
    }

    /**
     * Attempts to rectify malformed JSON responses.
     *
     * @param jsonInput   the malformed JSON input
     * @param format      the expected format
     * @param promptModel the prompt model containing configuration
     * @param auditId     the audit identifier for tracking
     * @return the rectified JSON string
     * @throws IllegalArgumentException if the chat response is null
     */
    private String rectifyJSON(String jsonInput, String format, PromptModel promptModel, String auditId) {
        log.error("error while parsing JSON string. trying to rectify JSON");
        Map<String, Object> model = Map.of("json", jsonInput, "format", format);
        PromptTemplate systemPromptTemplate = new PromptTemplate(rectifyJsonSystemPrompt);
        PromptTemplate userPromptTemplate = PromptTemplate.builder().resource(rectifyJsonUserPrompt).variables(model).build();
        String systemPrompt = systemPromptTemplate.getTemplate();
        String userPrompt = userPromptTemplate.render();

        ChatModel chatModel = inferenceManager.getChatModelByProvider(promptModel.getProvider());

        Instant startTime = Instant.now();
        ChatResponse chatResponse = ChatClient.create(chatModel)
                .prompt()
                .system(systemPrompt)
                .user(userPrompt)
                .options(OpenAiChatOptions.builder().model(defaultModel).build())
                .call()
                .chatResponse();

        Assert.notNull(chatResponse, "chat response is null");
        String content = chatResponse.getResult().getOutput().getText();
        Instant endTime = Instant.now();
        if (promptAuditEnabled) {
            //Audit related code
            long timeTakenMillis = Duration.between(startTime, endTime).toMillis();
            Usage usage = chatResponse.getMetadata().getUsage();

            PromptAudit promptAudit = PromptAudit.builder()
                    .applicationName(applicationName)
                    .chatOptions(AuditUtils.getChatOptionsMap(promptModel))
                    .auditId(AuditUtils.generateRectifyJsonAuditId(auditId))
                    .promptId(promptModel.getPromptId())
                    .model(promptModel.getModel())
                    .responseText(content)
                    .totalToken(usage.getTotalTokens())
                    .promptToken(usage.getPromptTokens())
                    .generationTokens(usage.getCompletionTokens())
                    .responseTime(timeTakenMillis)
                    .promptName(promptModel.getPromptName())
                    .creationTime(new Date())
                    .requestText(jsonInput)
                    .provider(promptModel.getProvider())
                    .status("success")
                    .build();

            auditService.sendPromptAudit(promptAudit);
        }
        return content;
    }

}
