package com.enttribe.commons.ai.config;

import org.springframework.web.client.RestTemplate;

/**
 * A singleton class that provides a shared {@link RestTemplate} instance across the application.
 * This class ensures that only one instance of {@link RestTemplate} is created and reused,
 * following the singleton design pattern.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RestTemplateSingleton {

    /**
     * The singleton instance of {@link RestTemplate}.
     */
    private static RestTemplate restTemplate;

    /**
     * Private constructor to prevent instantiation of this utility class.
     * This enforces the singleton pattern by making the class non-instantiable.
     */
    private RestTemplateSingleton() {
        // Private constructor to prevent instantiation
    }

    /**
     * Returns the singleton instance of {@link RestTemplate}.
     * If the instance doesn't exist, it creates a new one.
     *
     * @return the singleton {@link RestTemplate} instance
     */
    public static RestTemplate getRestTemplate() {
        if (restTemplate == null) {
            restTemplate = new RestTemplate();
        }
        return restTemplate;
    }
}
