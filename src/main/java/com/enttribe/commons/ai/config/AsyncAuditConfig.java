package com.enttribe.commons.ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Configuration
@EnableAsync
public class AsyncAuditConfig {

    @Bean(name = "auditTaskExecutor")
    public Executor auditTaskExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
}