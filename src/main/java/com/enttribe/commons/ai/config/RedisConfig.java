package com.enttribe.commons.ai.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.SslOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.io.File;
import java.util.List;

/**
 * Configuration class for Redis setup using Redis Sentinel for high availability.
 * This class provides beans for Redis connection and template configuration.
 * The configuration is only activated when the 'redis.enable' property is set to true.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "commons.ai.sdk.chat.redis.enable", havingValue = "true", matchIfMissing = false)
public class RedisConfig {

    private static final Logger log = LoggerFactory.getLogger(RedisConfig.class);

    @Value("${commons.ai.sdk.redis.sentinel.master:}")
    private String master;

    @Value("${commons.ai.sdk.redis.sentinel.nodes:}")
    private List<String> sentinelNodes;

    @Value("${commons.ai.sdk.redis.password:}")
    private String password;

    @Value("${commons.ai.sdk.chat.redis.type:sentinel}")
    private String redisType;

    @Value("${commons.ai.sdk.redis.host:localhost}")
    private String redisHost;

    @Value("${commons.ai.sdk.redis.port:6379}")
    private Integer redisPort;

    @Value("${commons.ai.sdk.chat.redis.trustStorePath:}")
    private String trustStorePath;

    @Value("${commons.ai.sdk.chat.redis.trustStorePassword:}")
    private String trustStorePassword;

    /**
     * Creates and configures a RedisTemplate bean for Redis operations.
     * Uses StringRedisSerializer for both keys and values to ensure proper string handling.
     *
     * @param connectionFactory the Redis connection factory to be used
     * @return configured RedisTemplate instance
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("Creating RedisTemplate bean with provided RedisConnectionFactory");
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);

        // Use String serializer for keys and hash keys
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        log.info("RedisTemplate configured with StringRedisSerializer for keys and values");
        return redisTemplate;
    }

    /**
     * Creates and configures a RedisConnectionFactory using Redis Sentinel configuration.
     * Sets up the master-slave topology and sentinel nodes for high availability.
     * Optionally configures password authentication if provided.
     *
     * @return configured LettuceConnectionFactory instance
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisConnectionFactory redisConnectionFactory() {
        log.info("Initializing RedisConnectionFactory. Redis type: {}", redisType);
        try {
            return switch (redisType) {
                case "standalone" -> standaloneRedisConnectionFactory();
                case "sentinel" -> sentinelRedisConnectionFactory();
                case "standalone_ssl" -> standaloneSslRedisConnectionFactory();
                default -> {
                    log.error("Invalid redis type: {}", redisType);
                    throw new IllegalArgumentException("Invalid redis type: " + redisType);
                }
            };
        } catch (Exception e) {
            log.error("Error initializing RedisConnectionFactory: {}", e.getMessage(), e);
            throw e;
        }
    }

    private RedisConnectionFactory standaloneRedisConnectionFactory() {
        log.info("Configuring standalone Redis connection. Host: {}, Port: {}", redisHost, redisPort);
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(redisHost, redisPort);
        if (password != null && !password.isEmpty()) {
            log.info("Standalone Redis: Password authentication is enabled");
            config.setPassword(password);
        } else {
            log.info("Standalone Redis: No password authentication");
        }
        return new LettuceConnectionFactory(config);
    }

    private RedisConnectionFactory sentinelRedisConnectionFactory() {
        log.info("Configuring Redis Sentinel connection. Master: {}", master);
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration().master(master);

        sentinelNodes.forEach(node -> {
            log.info("Adding sentinel node: {}", node);
            String[] parts = node.split(":");
            sentinelConfig.sentinel(parts[0], Integer.parseInt(parts[1]));
        });

        if (password != null && !password.isEmpty()) {
            log.info("Sentinel Redis: Password authentication is enabled");
            sentinelConfig.setPassword(password);
        } else {
            log.info("Sentinel Redis: No password authentication");
        }

        return new LettuceConnectionFactory(sentinelConfig);
    }

    private RedisConnectionFactory standaloneSslRedisConnectionFactory() {
        log.info("Configuring standalone SSL Redis connection. Host: {}, Port: {}", redisHost, redisPort);
        log.info("SSL trustStorePath: {}", trustStorePath);

        // Build SslOptions for Lettuce
        SslOptions sslOptions = SslOptions.builder()
                .jdkSslProvider()
                .truststore(new File(trustStorePath), trustStorePassword)
                .build();

        ClientOptions clientOptions = ClientOptions.builder()
                .sslOptions(sslOptions)
                .build();

        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(redisHost, redisPort);

        if (password != null && !password.isEmpty()) {
            log.info("Standalone SSL Redis: Password authentication is enabled");
            config.setPassword(password);
        } else {
            log.info("Standalone SSL Redis: No password authentication");
        }

        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                .useSsl()
                .disablePeerVerification()
                .and()
                .clientOptions(clientOptions)
                .build();

        return new LettuceConnectionFactory(config, clientConfig);
    }


}
