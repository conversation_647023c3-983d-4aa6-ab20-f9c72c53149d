package com.enttribe.commons.ai.config;

import org.springframework.util.Assert;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Utility class for managing and accessing application properties.
 * This class provides a centralized way to read properties from the application.properties file
 * with type-safe access methods and proper validation.
 * The properties are loaded once during class initialization from the application.properties file
 * in the classpath. If the file is not found or cannot be loaded, appropriate exceptions are thrown.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class AppProperties {
    private static final Properties properties = new Properties();

    static {
        try (InputStream input = AppProperties.class.getClassLoader().getResourceAsStream("application.properties")) {
            if (input == null) {
                throw new IllegalStateException("application.properties file not found in classpath");
            }
            properties.load(input);
        } catch (IOException ex) {
            throw new RuntimeException("Failed to load application.properties", ex);
        }
    }

    /**
     * Retrieves a property value for the given key.
     * Throws an exception if the property value is null or empty.
     *
     * @param key the property key
     * @return the property value
     * @throws IllegalArgumentException if the property value is null or empty
     */
    public static String getProperty(String key) {
        String propertyValue = properties.getProperty(key);
        Assert.hasText(propertyValue, String.format("value for key [%s] is not set", key));
        return propertyValue;
    }

    /**
     * Retrieves a JSON property value for the given key.
     * Returns an empty JSON array string "[]" if the property is not found.
     *
     * @param key the property key
     * @return the JSON property value or "[]" if not found
     */
    public static String getJsonProperty(String key) {
        String propertyValue = properties.getProperty(key);
        return propertyValue == null ? "[]" : propertyValue;
    }

    /**
     * Retrieves a property value for the given key with a default value if not found.
     *
     * @param key          the property key
     * @param defaultValue the default value to return if the property is not found
     * @return the property value or the default value if not found
     */
    public static String getProperty(String key, String defaultValue) {
        String propertyValue = properties.getProperty(key);
        return propertyValue == null ? defaultValue : propertyValue;
    }

    /**
     * Retrieves a property value and converts it to the specified target type.
     * Supports conversion to String, Integer, Long, Double, Boolean, and Float.
     *
     * @param <T>             the target type parameter
     * @param key             the property key
     * @param targetValueType the Class object of the target type
     * @return the property value converted to the target type
     * @throws IllegalArgumentException      if the property is not found or conversion fails
     * @throws UnsupportedOperationException if conversion to the target type is not supported
     */
    public static <T> T getProperty(String key, Class<T> targetValueType) {
        String value = properties.getProperty(key);
        if (value == null) {
            throw new IllegalArgumentException("Property with key '" + key + "' not found.");
        }

        try {
            if (targetValueType == String.class) {
                return targetValueType.cast(value);
            } else if (targetValueType == Integer.class) {
                return targetValueType.cast(Integer.parseInt(value));
            } else if (targetValueType == Long.class) {
                return targetValueType.cast(Long.parseLong(value));
            } else if (targetValueType == Double.class) {
                return targetValueType.cast(Double.parseDouble(value));
            } else if (targetValueType == Boolean.class) {
                return targetValueType.cast(Boolean.parseBoolean(value));
            } else if (targetValueType == Float.class) {
                return targetValueType.cast(Float.parseFloat(value));
            } else {
                throw new UnsupportedOperationException("Conversion to type " + targetValueType.getName() + " is not supported.");
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert property value to type " + targetValueType.getName(), e);
        }
    }
}
