package com.enttribe.commons.ai.chat.impl;

import com.enttribe.commons.ai.advisor.AuditAdvisor;
import com.enttribe.commons.ai.advisor.IntentEnhanceAdvisor;
import com.enttribe.commons.ai.advisor.LlmGuardAdvisor;
import com.enttribe.commons.ai.audit.tool.ToolAuditHelper;
import com.enttribe.commons.ai.chat.AiAgentModel;
import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.commons.ai.config.VectorStoreConfig;
import com.enttribe.commons.ai.manager.AdvisorManager;
import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.model.KnowledgeBase;
import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.Tool;
import com.enttribe.commons.ai.model.agent.AgentPrompt;
import com.enttribe.commons.ai.model.agent.AgentPromptV1;
import com.enttribe.commons.ai.model.audit.AuditContext;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import com.enttribe.commons.ai.rag.VectorService;
import com.enttribe.commons.ai.service.PromptApi;
import com.enttribe.commons.ai.service.ToolService;
import com.enttribe.commons.ai.util.AgentUtils;
import com.enttribe.commons.ai.util.ChatModelUtils;
import com.enttribe.commons.ai.util.JsonUtils;
import com.enttribe.commons.ai.util.KnowledgeBaseUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class AiAgentModelImpl implements AiAgentModel {

    @Value("${commons.ai.sdk.chat-size:15}")
    private Integer chatSize;

    @Value("${commons.ai.sdk.intent.metadata:metadata}")
    private String metadata;

    @Value("${commons.ai.sdk.intent.exact:0.9999}")
    private double exact;

    @Value("${commons.ai.sdk.intent.partial:0.85}")
    private double partial;

    @Value("${commons.ai.sdk.intent.whoEnabler:true}")
    private Boolean whoEnabler;

    @Value("${commons.ai.sdk.intent.whatEnabler:true}")
    private Boolean whatEnabler;

    private static final Logger log = LoggerFactory.getLogger(AiAgentModelImpl.class);
    private final PromptApi promptApi;
    private final InferenceManager inferenceManager;
    private final AdvisorManager advisorManager;
    private final ToolService toolService;
    private final VectorService vectorService;
    private final AiChatModel aiChatModel;
    private final AuditAdvisor auditAdvisor;
    private final LlmGuardAdvisor llmGuardAdvisor;
    private final VectorStoreConfig vectorStoreConfig;

    public AiAgentModelImpl(PromptApi promptApi, InferenceManager inferenceManager, AdvisorManager advisorManager,
                            ToolService toolService, VectorService vectorService, AiChatModel aiChatModel,
                            AuditAdvisor auditAdvisor, LlmGuardAdvisor llmGuardAdvisor, VectorStoreConfig vectorStoreConfig) {
        this.promptApi = promptApi;
        this.inferenceManager = inferenceManager;
        this.advisorManager = advisorManager;
        this.toolService = toolService;
        this.vectorService = vectorService;
        this.aiChatModel = aiChatModel;
        this.auditAdvisor = auditAdvisor;
        this.llmGuardAdvisor = llmGuardAdvisor;
        this.vectorStoreConfig = vectorStoreConfig;
    }


    @Override
    public ChatClient getChatClient(AgentPrompt agentPrompt) {
        Map<String, Object> toolContextWithAudit = agentPrompt.getToolContext();
        if (agentPrompt.getPromptId() != null) {
            PromptModel promptModel = this.promptApi.getPromptById(agentPrompt.getPromptId());
//            setToolAuditContext(promptModel, agentPrompt.getConversationId());
            toolContextWithAudit = addAuditContext(agentPrompt.getToolContext(), promptModel, agentPrompt.getConversationId());
        }

        String systemMessage = AgentUtils.getSystemMessage(agentPrompt.getSystemMessage(), agentPrompt.getVariableMap());
        List<Tool> tools = promptApi.getToolsByIds(agentPrompt.getTools());
        List<ToolCallback> registeredTools = toolService.registerToolCallback(tools);
        List<String> toolList = registeredTools.stream().map(toolCallback -> toolCallback.getToolDefinition().name()).toList();
        log.info("registered tools are : {}", toolList);

        ChatOptions chatOptions = AgentUtils.prepareChatOptions(agentPrompt);

        List<String> knowledgeBaseIds = agentPrompt.getKnowledgeBaseIds();
        List<ToolCallback> knowledgeBaseTools = new ArrayList<>();
        for (String knowledgeBaseId : knowledgeBaseIds) {
            KnowledgeBase knowledgeBase = promptApi.getKnowledgeBaseById(knowledgeBaseId);
            Assert.notNull(knowledgeBase, "knowledge base must not be null");
            ToolCallback knowledgeBaseTool = KnowledgeBaseUtils.getKnowledgeBaseTool(knowledgeBase, vectorService, aiChatModel);
            knowledgeBaseTools.add(knowledgeBaseTool);
        }
        ToolCallback[] knowledgeBaseToolsArray = knowledgeBaseTools.toArray(new ToolCallback[0]);
        List<String> knowledgeBaseNames = Arrays.stream(knowledgeBaseToolsArray).map(kb -> kb.getToolDefinition().name()).toList();
        log.info("registered knowledgeBases are : {}", knowledgeBaseNames);

        return ChatClient.builder(inferenceManager.getChatModelByProvider(agentPrompt.getProvider()))
                .defaultOptions(chatOptions)
                .defaultToolCallbacks(registeredTools)
                .defaultToolCallbacks(knowledgeBaseToolsArray)
                .defaultToolContext(toolContextWithAudit)
                .defaultAdvisors(llmGuardAdvisor, auditAdvisor)
                .defaultAdvisors(advisorManager.getChatMemoryAdvisor())
                .defaultAdvisors(ChatModelUtils.getChatAdvisorSpec(agentPrompt.getConversationId(), chatSize))
                .defaultSystem(systemMessage)
                .build();
    }

    @Override
    public ChatClient getChatClientV1(AgentPromptV1 agentPrompt) {
        PromptModel promptModel = this.promptApi.getPromptById(agentPrompt.getPromptId());
        promptModel.setAgentName(agentPrompt.getAgentName());

//        setToolAuditContext(promptModel, agentPrompt.getConversationId());

        String systemMessage = AgentUtils.getSystemMessage(promptModel, agentPrompt.getVariableMap());
        List<Tool> tools = this.promptApi.getToolsByIds(agentPrompt.getToolIds());
        List<ToolCallback> registeredTools = this.toolService.registerToolCallback(tools);
        registeredTools.addAll(agentPrompt.getMcpToolCallbacks());
        List<String> toolList = registeredTools.stream().map(toolCallback -> toolCallback.getToolDefinition().name()).toList();
        log.info("registered tools are : {}", toolList);

        List<ToolCallback> knowledgeBaseTools = new ArrayList<>();
        for (String knowledgeBaseId : agentPrompt.getKnowledgeBaseIds()) {
            KnowledgeBase knowledgeBase = this.promptApi.getKnowledgeBaseById(knowledgeBaseId);
            Assert.notNull(knowledgeBase, "knowledge base must not be null");
            ToolCallback knowledgeBaseTool = KnowledgeBaseUtils.getKnowledgeBaseTool(knowledgeBase, this.vectorService, this.aiChatModel);
            knowledgeBaseTools.add(knowledgeBaseTool);
        }

        Map<String, Object> toolContextWithAudit = addAuditContext(agentPrompt.getToolContext(), promptModel, agentPrompt.getConversationId());

        ToolCallback[] knowledgeBaseToolsArray = knowledgeBaseTools.toArray(new ToolCallback[0]);
        List<String> knowledgeBaseNames = Arrays.stream(knowledgeBaseToolsArray).map((kb) -> kb.getToolDefinition().name()).toList();
        log.info("registered knowledgeBases are : {}", knowledgeBaseNames);
        ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel, agentPrompt.getModel());

        if (agentPrompt.getShouldEnhanceIntent()) {
            VectorMetaData vectorMetaData;
            String decodedMetadata = null;
            try {
                log.info("intent metadata before : {}", metadata);

                decodedMetadata = new String(Base64.getDecoder().decode(metadata));
                log.info("intent metadata after decoding : {}", decodedMetadata);
                Map<String, String> map = JsonUtils.getObjectMapper().readValue(decodedMetadata, Map.class);
                vectorMetaData = VectorMetaData.fromMap(map);

                log.info("vector metadata used is : {}", vectorMetaData);
            } catch (JsonProcessingException e) {
                log.error("error in parsing intent metadata : {}", decodedMetadata, e);
                throw new RuntimeException(e);
            }


            log.info("Here are Newly Assigned Values Values\n EXACT : {} \n PARTIAL : {} \n WHO_ENABLER : {} \n WHAT_ENABLER : {} ", exact, partial, whoEnabler, whatEnabler );
//            VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorMetaData);
            IntentEnhanceAdvisor intentEnhanceAdvisor = IntentEnhanceAdvisor.builder()
                    .order(799)
                    .exact(exact)
                    .partial(partial)
                    .whoEnabler(whoEnabler)
                    .whatEnabler(whatEnabler)
                    .build();

            return ChatClient.builder(this.inferenceManager.getChatModelByProvider(promptModel.getProvider()))
                    .defaultOptions(chatOptions)
                    .defaultToolCallbacks(registeredTools)
                    .defaultToolCallbacks(knowledgeBaseToolsArray)
                    .defaultAdvisors(llmGuardAdvisor)
                    .defaultAdvisors(this.advisorManager.getChatMemoryAdvisor(), auditAdvisor, intentEnhanceAdvisor)
                    .defaultAdvisors(ChatModelUtils.getChatAdvisorSpec(agentPrompt.getConversationId(), this.chatSize))
                    .defaultToolContext(toolContextWithAudit)
                    .defaultSystem(systemMessage)
                    .build();
        }

        return ChatClient.builder(this.inferenceManager.getChatModelByProvider(promptModel.getProvider()))
                .defaultOptions(chatOptions)
                .defaultToolCallbacks(registeredTools)
                .defaultToolCallbacks(knowledgeBaseToolsArray)
                .defaultAdvisors(llmGuardAdvisor)
                .defaultAdvisors(this.advisorManager.getChatMemoryAdvisor(), auditAdvisor)
                .defaultAdvisors(ChatModelUtils.getChatAdvisorSpec(agentPrompt.getConversationId(), this.chatSize))
                .defaultToolContext(toolContextWithAudit)
                .defaultSystem(systemMessage)
                .build();
    }

    @Override
    public ChatClient getChatClient(String promptId, List<String> toolIds, List<String> knowledgeBaseIds, Map<String, Object> variableMap, Map<String, Object> toolContext, String conversationId) {
        PromptModel promptModel = promptApi.getPromptById(promptId);
//        setToolAuditContext(promptModel, conversationId);
        String systemMessage = AgentUtils.getSystemMessage(promptModel, variableMap);
        List<Tool> tools = promptApi.getToolsByIds(toolIds);
        List<ToolCallback> registeredTools = toolService.registerToolCallback(tools);
        List<String> toolList = registeredTools.stream().map(toolCallback -> toolCallback.getToolDefinition().name()).toList();
        log.info("registered tools are : {}", toolList);


        ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel);

        List<ToolCallback> knowledgeBaseTools = new ArrayList<>();
        for (String knowledgeBaseId : knowledgeBaseIds) {
            KnowledgeBase knowledgeBase = promptApi.getKnowledgeBaseById(knowledgeBaseId);
            Assert.notNull(knowledgeBase, "knowledge base must not be null");
            ToolCallback knowledgeBaseTool = KnowledgeBaseUtils.getKnowledgeBaseTool(knowledgeBase, vectorService, aiChatModel);
            knowledgeBaseTools.add(knowledgeBaseTool);
        }
        ToolCallback[] knowledgeBaseToolsArray = knowledgeBaseTools.toArray(new ToolCallback[0]);
        List<String> knowledgeBaseNames = Arrays.stream(knowledgeBaseToolsArray).map(kb -> kb.getToolDefinition().name()).toList();
        log.info("registered knowledgeBases are : {}", knowledgeBaseNames);

        Map<String, Object> toolContextWithAudit = addAuditContext(toolContext, promptModel, conversationId);
        return ChatClient.builder(inferenceManager.getChatModelByProvider(promptModel.getProvider()))
                .defaultOptions(chatOptions)
                .defaultToolCallbacks(registeredTools)
                .defaultToolCallbacks(knowledgeBaseToolsArray)
                .defaultAdvisors(advisorManager.getChatMemoryAdvisor(), auditAdvisor)
                .defaultAdvisors(llmGuardAdvisor)
                .defaultToolContext(toolContextWithAudit)
                .defaultAdvisors(ChatModelUtils.getChatAdvisorSpec(conversationId, chatSize))
                .defaultSystem(systemMessage)
                .build();
    }

    @Override
    public ChatClient getChatClient(String promptId, List<String> toolIds,
                                    List<String> knowledgeBaseIds, Map<String, Object> variableMap) {
        PromptModel promptModel = promptApi.getPromptById(promptId);
        String systemMessage = AgentUtils.getSystemMessage(promptModel, variableMap);
        List<Tool> tools = promptApi.getToolsByIds(toolIds);
        List<ToolCallback> registeredTools = toolService.registerToolCallback(tools);
        List<String> toolList = registeredTools.stream().map(toolCallback -> toolCallback.getToolDefinition().name()).toList();
        log.info("registered tools are : {}", toolList);

        ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel);

        List<ToolCallback> knowledgeBaseTools = new ArrayList<>();
        for (String knowledgeBaseId : knowledgeBaseIds) {
            KnowledgeBase knowledgeBase = promptApi.getKnowledgeBaseById(knowledgeBaseId);
            Assert.notNull(knowledgeBase, "knowledge base must not be null");
            ToolCallback knowledgeBaseTool = KnowledgeBaseUtils.getKnowledgeBaseTool(knowledgeBase, vectorService, aiChatModel);
            knowledgeBaseTools.add(knowledgeBaseTool);
        }
        ToolCallback[] knowledgeBaseToolsArray = knowledgeBaseTools.toArray(new ToolCallback[0]);
        List<String> knowledgeBaseNames = Arrays.stream(knowledgeBaseToolsArray).map(kb -> kb.getToolDefinition().name()).toList();
        log.info("registered knowledgeBases are : {}", knowledgeBaseNames);

        Map<String, Object> toolContext = addAuditContext(Map.of(), promptModel, null);
        return ChatClient.builder(inferenceManager.getChatModelByProvider(promptModel.getProvider()))
                .defaultOptions(chatOptions)
                .defaultToolCallbacks(registeredTools)
                .defaultToolCallbacks(knowledgeBaseToolsArray)
                .defaultToolContext(toolContext)
                .defaultAdvisors(llmGuardAdvisor)
                .defaultAdvisors(advisorManager.getChatMemoryAdvisor(), auditAdvisor)
                .defaultSystem(systemMessage)
                .build();
    }

    @Override
    public ChatClient getChatClient(String promptId, List<String> toolIds, List<String> knowledgeBaseIds, List<ToolCallback> mcpToolCallbacks, Map<String, Object> variableMap, Map<String, Object> toolContext, String conversationId) {
        PromptModel promptModel = this.promptApi.getPromptById(promptId);
//        setToolAuditContext(promptModel, conversationId);
        String systemMessage = AgentUtils.getSystemMessage(promptModel, variableMap);
        List<Tool> tools = this.promptApi.getToolsByIds(toolIds);
        List<ToolCallback> registeredTools = this.toolService.registerToolCallback(tools);
        registeredTools.addAll(mcpToolCallbacks);
        List<String> toolList = registeredTools.stream().map(toolCallback -> toolCallback.getToolDefinition().name()).toList();
        log.info("registered tools are : {}", toolList);

        List<ToolCallback> knowledgeBaseTools = new ArrayList<>();
        for (String knowledgeBaseId : knowledgeBaseIds) {
            KnowledgeBase knowledgeBase = this.promptApi.getKnowledgeBaseById(knowledgeBaseId);
            Assert.notNull(knowledgeBase, "knowledge base must not be null");
            ToolCallback knowledgeBaseTool = KnowledgeBaseUtils.getKnowledgeBaseTool(knowledgeBase, this.vectorService, this.aiChatModel);
            knowledgeBaseTools.add(knowledgeBaseTool);
        }

        Map<String, Object> toolContextWithAudit = addAuditContext(toolContext, promptModel, conversationId);

        ToolCallback[] knowledgeBaseToolsArray = (ToolCallback[]) knowledgeBaseTools.toArray(new ToolCallback[0]);
        List<String> knowledgeBaseNames = Arrays.stream(knowledgeBaseToolsArray).map((kb) -> kb.getToolDefinition().name()).toList();
        log.info("registered knowledgeBases are : {}", knowledgeBaseNames);
        ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel);
        return ChatClient.builder(this.inferenceManager.getChatModelByProvider(promptModel.getProvider()))
                .defaultOptions(chatOptions)
                .defaultToolCallbacks(registeredTools)
                .defaultToolCallbacks(knowledgeBaseToolsArray)
                .defaultAdvisors(advisorManager.getChatMemoryAdvisor(), auditAdvisor)
                .defaultAdvisors(llmGuardAdvisor)
                .defaultAdvisors(ChatModelUtils.getChatAdvisorSpec(conversationId, this.chatSize))
                .defaultToolContext(toolContextWithAudit)
                .defaultSystem(systemMessage)
                .build();
    }

    private Map<String, Object> addAuditContext(Map<String, Object> toolContext, PromptModel promptModel, String conversationId) {
        conversationId = conversationId == null ? UUID.randomUUID().toString() : conversationId;
        AuditContext auditContext = AuditContext.builder()
                .promptModel(promptModel)
                .auditId(conversationId)
                .build();

        ToolAuditHelper.ToolAuditContext toolAuditContext = ToolAuditHelper.ToolAuditContext.builder()
                .auditId(String.format("%s_%s", promptModel.getApplicationName(), conversationId))
                .agentName(promptModel.getAgentName())
                .promptName(promptModel.getPromptName())
                .promptId(promptModel.getId())
                .applicationName(promptModel.getApplicationName())
                .model(promptModel.getModel())
                .provider(promptModel.getProvider())
                .status("success")
                .build();

        Map<String, Object> mutableContext = new HashMap<>(toolContext);
        mutableContext.put("auditContext", auditContext);
        mutableContext.put("toolAuditContext", toolAuditContext);
        return mutableContext;
    }

    @Override
    public void deleteDataFromMemory(String conversationId) {
        log.info("deleting data from redis with Key : {}", conversationId);
        ChatMemory chatMemory = advisorManager.getChatMemory();
        chatMemory.clear(conversationId);
    }

    @Override
    public List<Message> getMessagesFromMemory(String conversationId, int lastN) {
        log.info("fetching data from redis with Key : {}", conversationId);
        ChatMemory chatMemory = advisorManager.getChatMemory();
        List<Message> allMessages = chatMemory.get(conversationId);
        return allMessages.stream()
                .skip(Math.max(0, allMessages.size() - lastN))
                .toList();
    }

    @Override
    public void addDataInChatMemory(String conversationId, List<Message> messages) {
        log.info("adding data in chat memory for Key : {}", conversationId);
        ChatMemory chatMemory = advisorManager.getChatMemory();
        chatMemory.add(conversationId, messages);
        log.info("data successfully added in chat memory");
    }


}
