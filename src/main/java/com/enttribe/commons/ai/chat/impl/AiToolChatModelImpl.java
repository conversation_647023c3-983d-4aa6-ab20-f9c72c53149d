package com.enttribe.commons.ai.chat.impl;

import com.enttribe.commons.ai.chat.AiToolChatModel;
import com.enttribe.commons.ai.service.GenericService;
import com.enttribe.commons.ai.util.AuditUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * Ai tool chat model. Provide chat completion APIs with tool support.
 *
 * <AUTHOR>
 */
@Service
public class AiToolChatModelImpl implements AiToolChatModel {

    /**
     * Instantiates a new Ai tool chat model.
     *
     * @param genericService the generic service
     */
    public AiToolChatModelImpl(GenericService genericService) {
        this.genericService = genericService;
    }

    private final GenericService genericService;


    @Override
    public <T> T chatCompletion(String promptId, Set<String> tools, Map<String, Object> variableMap, Class<T> format) {
        return genericService.getGenericResponse(promptId, tools, variableMap, format, AuditUtils.getAuditId(), null);
    }

    @Override
    public <T> T chatCompletion(String promptId, String auditId, Set<String> tools, Map<String, Object> variableMap, Class<T> format) {
        return genericService.getGenericResponse(promptId, tools, variableMap, format, auditId, null);
    }

    @Override
    public <T> T chatCompletion(String promptId, Set<String> tools, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, tools, variableMap, typeReference, AuditUtils.getAuditId(), null);
    }

    @Override
    public <T> T chatCompletion(String promptId, String auditId, Set<String> tools, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, tools, variableMap, typeReference, auditId, null);
    }

    @Override
    public <T> T chatCompletion(String promptId, Set<String> tools, Map<String, Object> variableMap, String chatId, Class<T> format) {
        return genericService.getGenericResponse(promptId, tools, variableMap, format, AuditUtils.getAuditId(), chatId);
    }

}
