package com.enttribe.commons.ai.chat;

import com.enttribe.commons.ai.model.agent.AgentPrompt;
import com.enttribe.commons.ai.model.agent.AgentPromptV1;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;
import java.util.Map;

/**
 * Interface defining operations for AI agent model interactions.
 * This interface provides methods to create and manage chat clients with various configurations,
 * including prompt management, tool integration, and conversation memory handling.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AiAgentModel {


    /**
     * Creates a chat client using a predefined agent prompt configuration. Also uses return direct property.
     *
     * @param agentPrompt The agent prompt configuration object
     * @return ChatClient configured with the agent prompt
     */
    ChatClient getChatClient(AgentPrompt agentPrompt);

    /**
     * Creates a chat client using a predefined agent prompt configuration. Also uses return direct property.
     *
     * @param agentPrompt The agent prompt configuration object
     * @return ChatClient configured with the agent prompt
     */
    ChatClient getChatClientV1(AgentPromptV1 agentPrompt);

    /**
     * Creates a chat client with multiple knowledge bases and variable substitution.
     *
     * @param promptId         Unique identifier for the prompt template
     * @param toolIds          List of tool identifiers
     * @param knowledgeBaseIds List of knowledge base identifiers
     * @param variableMap      Map of variables and their values for prompt template substitution
     * @param toolContext      the tool context
     * @param conversationId   Unique identifier to maintain conversation memory
     * @return ChatClient configured with multiple knowledge bases and variables
     */
    ChatClient getChatClient(String promptId, List<String> toolIds, List<String> knowledgeBaseIds, Map<String, Object> variableMap, Map<String, Object> toolContext, String conversationId);

    /**
     * Creates a chat client with multiple knowledge bases and variable substitution without conversation memory.
     *
     * @param promptId         Unique identifier for the prompt template
     * @param toolIds          List of tool identifiers
     * @param knowledgeBaseIds List of knowledge base identifiers
     * @param variableMap      Map of variables and their values for prompt template substitution
     * @return ChatClient configured with multiple knowledge bases and variables
     */
    ChatClient getChatClient(String promptId, List<String> toolIds, List<String> knowledgeBaseIds, Map<String, Object> variableMap);

    /**
     * Gets chat client with MCP tools.
     *
     * @param promptId         the prompt id
     * @param toolIds          the tool ids
     * @param knowledgeBaseIds the knowledge base ids
     * @param mcpToolCallbacks the mcp tool callbacks
     * @param variableMap      the variable map
     * @param toolContext      the tool context
     * @param conversationId   the conversation id
     * @return the chat client
     */
    ChatClient getChatClient(String promptId, List<String> toolIds, List<String> knowledgeBaseIds, List<ToolCallback> mcpToolCallbacks, Map<String, Object> variableMap, Map<String, Object> toolContext, String conversationId);


    /**
     * Deletes conversation memory data from Redis for a specific conversation.
     *
     * @param conversationId Unique identifier of the conversation whose data should be deleted
     */
    void deleteDataFromMemory(String conversationId);

    /**
     * Retrieves the last N messages from the conversation memory.
     *
     * @param conversationId Unique identifier of the conversation
     * @param lastN          Number of most recent messages to retrieve
     * @return List of the last N messages from the conversation
     */
    List<Message> getMessagesFromMemory(String conversationId, int lastN);

    /**
     * Add data in chat memory.
     *
     * @param conversationId the conversation id
     * @param messages       the messages
     */
    void addDataInChatMemory(String conversationId, List<Message> messages);

}
