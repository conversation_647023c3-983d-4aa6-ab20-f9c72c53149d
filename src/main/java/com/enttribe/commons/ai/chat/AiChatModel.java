package com.enttribe.commons.ai.chat;

import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.core.ParameterizedTypeReference;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * The {@code AiChatModel} interface defines methods for generating chat completions
 * using various inputs, such as prompts, user messages, and configuration options.
 * These methods support different result formats and allow customization via
 * variable maps and options.
 *
 * <p><b>Note:</b> The caller of this API must include a variable named {@code format}
 * in the provided prompt to get the output in desired type {@code T}. For example:
 * <pre>
 * Example : "Provide a summary in {format}.";
 * </pre>
 *
 * <AUTHOR>
 */
public interface AiChatModel {

    /**
     * Generates a chat completion based on the user's message.
     *
     * @param userMessage the message provided by the user
     * @return a {@code String} representing the chat response
     */
    String chatCompletion(String userMessage);


    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders in user message.
     *
     * @param promptId    the identifier of the saved prompt
     * @param variableMap a map containing values for variables in the user prompt
     * @return the string
     */
    String chatCompletion(String promptId, Map<String, Object> variableMap);

    /**
     * Generates a chat completion using a saved prompt ID.
     *
     * @param <T>      the type of the result
     * @param promptId the identifier of the saved prompt
     * @param format   the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>      the type of the result
     * @param promptId the identifier of the saved prompt
     * @param auditId  the audit id used to group together related prompt and exception audit
     * @param format   the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, String auditId, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID and a type reference for complex types.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID and a type reference for complex types.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param auditId       the audit id used to group together related prompt and exception audit
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, String auditId, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt
     * @param variableMap a map containing values for variables in the prompt (e.g., system or user variables)
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Map<String, Object> variableMap, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID, chatId and a variable map for placeholders.
     * This api also remembers the context for the given chatId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt
     * @param chatId      the chat id for which the conversation is maintained in memory.
     * @param variableMap a map containing values for variables in the prompt (e.g., system or user variables)
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, String chatId, Map<String, Object> variableMap, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID, chatId and a variable map for placeholders.
     * This api also remembers the context for the given chatId.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param chatId        the chat id for which the conversation is maintained in memory.
     * @param variableMap   a map containing values for variables in the prompt (e.g., system or user variables)
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, String chatId, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt
     * @param variableMap a map containing values for variables in the prompt (e.g., system or user variables)
     * @param auditId     the audit id used to group together related prompt and exception audit
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Map<String, Object> variableMap, String auditId, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID, a variable map, and a type reference.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param variableMap   a map containing values for variables in the prompt
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID, a variable map, and a type reference.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param variableMap   a map containing values for variables in the prompt
     * @param auditId       the audit id used to group together related prompt and exception audit
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Map<String, Object> variableMap, String auditId, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders of system prompt.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt. Here only system prompt associated with the promptId is taken.
     * @param messages    the messages. Other than system prompt.
     * @param variableMap a map containing values for variables in the prompt (e.g., only system variables)
     * @param auditId     the audit id used to group together related prompt and exception audit
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletionX101(String promptId, List<Message> messages, Map<String, Object> variableMap, String auditId, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders of system prompt.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt. Here only system prompt associated with the promptId is taken.
     * @param messages    the messages. Other than system prompt.
     * @param variableMap a map containing values for variables in the prompt (e.g., only system variables)
     * @param auditId     the audit id used to group together related prompt and exception audit
     * @param allowGuard  the allow guard option, if false then LlmGuardAdvisor is not used.
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletionX101(String promptId, List<Message> messages, Map<String, Object> variableMap, String auditId, boolean allowGuard, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders of system prompt.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt. Here only system prompt associated with the promptId is taken.
     * @param messages    the messages. Other than system prompt.
     * @param variableMap a map containing values for variables in the prompt (e.g., only system variables)
     * @param chatOptions the chat options
     * @param auditId     the audit id used to group together related prompt and exception audit
     * @param allowGuard  the allow guard option, if false then LlmGuardAdvisor is not used.
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletionX101(String promptId, List<Message> messages, Map<String, Object> variableMap, ChatOptions chatOptions, String auditId, boolean allowGuard, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID and a variable map for placeholders of system prompt.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt. Here only system prompt associated with the promptId is taken.
     * @param messages    the messages. Other than system prompt.
     * @param toolIds     the tool ids
     * @param variableMap a map containing values for variables in the prompt (e.g., only system variables)
     * @param auditId     the audit id used to group together related prompt and exception audit
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletionX101(String promptId, List<Message> messages, List<String> toolIds, Map<String, Object> variableMap, String auditId, Class<T> format);

    /**
     * Gets variables of system prompt.
     *
     * @param promptId the identifier of the saved prompt. Here only system prompt associated with the promptId is taken.
     * @return the variables of system prompt
     */
    Set<String> getVariablesOfPromptX101(String promptId);

    /**
     * Refreshes the SDK maps as done on application start-up. It refreshed chat model map, embedding model map, prompt models and vector store map.
     */
    void refresh();

}
