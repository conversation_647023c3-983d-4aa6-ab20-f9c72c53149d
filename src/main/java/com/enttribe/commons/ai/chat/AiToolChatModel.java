package com.enttribe.commons.ai.chat;

import org.springframework.core.ParameterizedTypeReference;

import java.util.Map;
import java.util.Set;

/**
 * Ai tool chat model. Provide chat completion APIs with tool support.
 *
 * <AUTHOR>
 */
public interface AiToolChatModel {

    /**
     * Generates a chat completion using a saved prompt ID, collection of tools (name of tools) and a variable map for placeholders.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt
     * @param tools       the tools (name of tools given in Set of String)
     * @param variableMap a map containing values for variables in the prompt (e.g., system or user variables)
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Set<String> tools, Map<String, Object> variableMap, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID, collection of tools (name of tools) and a variable map for placeholders.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt
     * @param auditId     the audit id used to group together related prompt and exception audit
     * @param tools       the tools (name of tools given in Set of String)
     * @param variableMap a map containing values for variables in the prompt (e.g., system or user variables)
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, String auditId, Set<String> tools, Map<String, Object> variableMap, Class<T> format);

    /**
     * Generates a chat completion using a saved prompt ID, collection of tools (name of tools) and a variable map for placeholders.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param tools         the tools (name of tools given in Set of String)
     * @param variableMap   a map containing values for variables in the prompt (e.g., system or user variables)
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Set<String> tools, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID, collection of tools (name of tools) and a variable map for placeholders.
     * Also groups related prompt and exception audit using input parameter auditId.
     *
     * @param <T>           the type of the result
     * @param promptId      the identifier of the saved prompt
     * @param auditId       the audit id used to group together related prompt and exception audit
     * @param tools         the tools (name of tools given in Set of String)
     * @param variableMap   a map containing values for variables in the prompt (e.g., system or user variables)
     * @param typeReference the type reference for specifying the result's structure
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, String auditId, Set<String> tools, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference);

    /**
     * Generates a chat completion using a saved prompt ID, collection of tools (name of tools) and a variable map for placeholders.
     * This api also remembers the context for the given chatId.
     *
     * @param <T>         the type of the result
     * @param promptId    the identifier of the saved prompt
     * @param tools       the tools (name of tools given in Set of String)
     * @param variableMap a map containing values for variables in the prompt (e.g., system or user variables)
     * @param chatId      the chat id for which the conversation is maintained in memory.
     * @param format      the class type of the result
     * @return an object of type {@code T} representing the formatted response
     */
    <T> T chatCompletion(String promptId, Set<String> tools, Map<String, Object> variableMap, String chatId, Class<T> format);


}