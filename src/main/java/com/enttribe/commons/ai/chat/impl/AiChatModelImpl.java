package com.enttribe.commons.ai.chat.impl;


import com.enttribe.commons.ai.config.AppProperties;
import com.enttribe.commons.ai.config.VectorStoreConfig;
import com.enttribe.commons.ai.manager.PromptManager;
import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.service.GenericService;
import com.enttribe.commons.ai.util.AuditUtils;
import com.enttribe.commons.ai.util.ChatModelUtils;
import com.enttribe.commons.ai.util.TemplateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Ai chat model. Provide chat completion APIs.
 *
 * <AUTHOR>
 */
@Service
public class AiChatModelImpl implements AiChatModel {

    private static final String defaultModel = AppProperties.getProperty("commons.ai.sdk.default.llm.model", "llama-3.3-70b-versatile");
    private static final Logger log = LoggerFactory.getLogger(AiChatModelImpl.class);

    /**
     * Instantiates a new Ai chat model.
     *
     * @param promptManager    the prompt manager
     * @param inferenceManager the inference manager
     */
    public AiChatModelImpl(PromptManager promptManager, InferenceManager inferenceManager,
                           GenericService genericService, VectorStoreConfig vectorStoreConfig) {
        this.promptManager = promptManager;
        this.inferenceManager = inferenceManager;
        this.genericService = genericService;
        this.vectorStoreConfig = vectorStoreConfig;
    }

    private final PromptManager promptManager;
    private final InferenceManager inferenceManager;
    private final GenericService genericService;
    private final VectorStoreConfig vectorStoreConfig;


    public String chatCompletion(String userMessage) {
        ChatModel chatModel = inferenceManager.getChatModelByProvider("groq");

        return ChatClient.create(chatModel)
                .prompt()
                .user(userMessage)
                .options(OpenAiChatOptions.builder().model(defaultModel).build())
                .call()
                .content();
    }

    @Override
    public String chatCompletion(String promptId, Map<String, Object> variableMap) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);

        List<Message> messageList = ChatModelUtils.getMessageListV1(promptModel, variableMap);

        String auditId = AuditUtils.getAuditId();
        return genericService.getLlmResponseAndDoPromptAudit(promptModel, messageList, auditId);
    }

    @Override
    public <T> T chatCompletionX101(String promptId, List<Message> messages, Map<String, Object> variableMap, String auditId, Class<T> format) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);

        BeanOutputConverter<T> outputConverter = new BeanOutputConverter<>(format);
        Map<String, Object> mutableMap = new HashMap<>(variableMap);
        mutableMap.put("format", outputConverter.getFormat());

        SystemMessage systemMessage = ChatModelUtils.getSystemMessageX101(promptModel, mutableMap);
        List<Message> messageList = new ArrayList<>();
        messageList.add(systemMessage);
        messageList.addAll(messages);

        String result = genericService.getLlmResponseAndDoPromptAuditX101(promptModel, List.of(), messageList, null, auditId, true);
        if (format.isAssignableFrom(String.class)) return (T) result;
        return genericService.getTypedResponse(outputConverter, result, promptModel, auditId);
    }

    @Override
    public <T> T chatCompletionX101(String promptId, List<Message> messages, Map<String, Object> variableMap, String auditId, boolean allowGuard, Class<T> format) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);

        BeanOutputConverter<T> outputConverter = new BeanOutputConverter<>(format);
        Map<String, Object> mutableMap = new HashMap<>(variableMap);
        mutableMap.put("format", outputConverter.getFormat());

        SystemMessage systemMessage = ChatModelUtils.getSystemMessageX101(promptModel, mutableMap);
        List<Message> messageList = new ArrayList<>();
        messageList.add(systemMessage);
        messageList.addAll(messages);

        String result = genericService.getLlmResponseAndDoPromptAuditX101(promptModel, List.of(), messageList, null, auditId, allowGuard);
        if (format.isAssignableFrom(String.class)) return (T) result;
        return genericService.getTypedResponse(outputConverter, result, promptModel, auditId);
    }

    @Override
    public <T> T chatCompletionX101(String promptId, List<Message> messages, Map<String, Object> variableMap, ChatOptions chatOptions, String auditId, boolean allowGuard, Class<T> format) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);

        BeanOutputConverter<T> outputConverter = new BeanOutputConverter<>(format);
        Map<String, Object> mutableMap = new HashMap<>(variableMap);
        mutableMap.put("format", outputConverter.getFormat());

        SystemMessage systemMessage = ChatModelUtils.getSystemMessageX101(promptModel, mutableMap);
        List<Message> messageList = new ArrayList<>();
        messageList.add(systemMessage);
        messageList.addAll(messages);

        String result = genericService.getLlmResponseAndDoPromptAuditX101(promptModel, List.of(), messageList, chatOptions, auditId, allowGuard);
        if (format.isAssignableFrom(String.class)) return (T) result;
        return genericService.getTypedResponse(outputConverter, result, promptModel, auditId);
    }

    @Override
    public <T> T chatCompletionX101(String promptId, List<Message> messages, List<String> toolIds, Map<String, Object> variableMap, String auditId, Class<T> format) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);

        BeanOutputConverter<T> outputConverter = new BeanOutputConverter<>(format);
        Map<String, Object> mutableMap = new HashMap<>(variableMap);
        mutableMap.put("format", outputConverter.getFormat());

        SystemMessage systemMessage = ChatModelUtils.getSystemMessageX101(promptModel, mutableMap);
        List<Message> messageList = new ArrayList<>();
        messageList.add(systemMessage);
        messageList.addAll(messages);

        String result = genericService.getLlmResponseAndDoPromptAuditX101(promptModel, toolIds, messageList, null, auditId, true);
        if (format.isAssignableFrom(String.class)) return (T) result;
        return genericService.getTypedResponse(outputConverter, result, promptModel, auditId);
    }

    @Override
    public Set<String> getVariablesOfPromptX101(String promptId) {
        PromptModel promptModel = promptManager.getPromptModel(promptId);

        return TemplateUtils.getVariablesOfPromptX101(promptModel);
    }

    @Override
    public void refresh() {
        log.info("refreshing manually...");
        promptManager.loadPrompts();
        inferenceManager.initializeChatModels();
        vectorStoreConfig.init();
        log.info("SDK refresh complete");
    }

    @Override
    public <T> T chatCompletion(String promptId, Class<T> format) {
        return genericService.getGenericResponse(promptId, null, null, format, AuditUtils.getAuditId(), null);
    }

    @Override
    public <T> T chatCompletion(String promptId, String auditId, Class<T> format) {
        return genericService.getGenericResponse(promptId, null, null, format, auditId, null);
    }

    @Override
    public <T> T chatCompletion(String promptId, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, null, null, typeReference, AuditUtils.getAuditId(), null);
    }

    @Override
    public <T> T chatCompletion(String promptId, String auditId, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, null, null, typeReference, auditId, null);
    }

    @Override
    public <T> T chatCompletion(String promptId, Map<String, Object> variableMap, Class<T> format) {
        return genericService.getGenericResponse(promptId, null, variableMap, format, AuditUtils.getAuditId(), null);
    }

    @Override
    public <T> T chatCompletion(String promptId, String chatId, Map<String, Object> variableMap, Class<T> format) {
        return genericService.getGenericResponse(promptId, null, variableMap, format, AuditUtils.getAuditId(), chatId);
    }

    @Override
    public <T> T chatCompletion(String promptId, String chatId, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, null, variableMap, typeReference, AuditUtils.getAuditId(), chatId);
    }

    @Override
    public <T> T chatCompletion(String promptId, Map<String, Object> variableMap, String auditId, Class<T> format) {
        return genericService.getGenericResponse(promptId, null, variableMap, format, auditId, null);
    }

    @Override
    public <T> T chatCompletion(String promptId, Map<String, Object> variableMap, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, null, variableMap, typeReference, AuditUtils.getAuditId(), null);
    }

    @Override
    public <T> T chatCompletion(String promptId, Map<String, Object> variableMap, String auditId, ParameterizedTypeReference<T> typeReference) {
        return genericService.getGenericResponse(promptId, null, variableMap, typeReference, auditId, null);
    }

}
