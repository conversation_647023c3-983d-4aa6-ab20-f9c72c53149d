package com.enttribe.commons.ai.audit;

import com.enttribe.commons.ai.exception.AiException;
import com.enttribe.commons.ai.exception.ErrorAuditException;
import com.enttribe.commons.ai.model.audit.ExceptionAudit;
import com.enttribe.commons.ai.util.ExceptionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.openai.api.common.OpenAiApiClientErrorException;
import org.springframework.ai.retry.NonTransientAiException;
import org.springframework.ai.retry.TransientAiException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * Aspect for auditing exceptions in AI chat operations.
 * This aspect intercepts method calls in the AI chat implementation and handles exceptions
 * by logging them and creating audit records. It is conditionally enabled based on the
 * 'exception.audit.enable' property.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Aspect
@Component
@ConditionalOnProperty(name = "exception.audit.enable", havingValue = "true", matchIfMissing = false)
public class AuditAspect {

    private static final Logger log = LoggerFactory.getLogger(AuditAspect.class);

    /**
     * The name of the application using the AI commons SDK.
     */
    @Value("${commons.ai.sdk.app.name}")
    private String applicationName;

    private final AuditService auditService;

    /**
     * Constructs a new AuditAspect with the required dependencies.
     *
     * @param auditService the service responsible for sending audit records
     */
    public AuditAspect(AuditService auditService) {
        this.auditService = auditService;
    }

    /**
     * Handles exceptions for all methods in AiChatModelImpl.
     * This advice wraps the method execution, catches any exceptions, logs them,
     * creates an audit record, and throws a wrapped ErrorAuditException.
     *
     * @param joinPoint the join point representing the intercepted method call
     * @return the result of the method execution if successful
     * @throws ErrorAuditException when an exception occurs during method execution
     */
    @Around("execution(* com.enttribe.commons.ai.chat..*(..))")
    public Object handleException(ProceedingJoinPoint joinPoint) {
        try {
            return joinPoint.proceed();
        } catch (Throwable ex) {
            // Access method arguments
            Object[] args = joinPoint.getArgs();
            String promptId = extractPromptId(args);
            // Log the error with the extracted value
            log.error("Exception occurred in method: {} with promptId: {}",
                    joinPoint.getSignature().toShortString(),
                    promptId,
                    ex);

            ExceptionAudit exceptionAudit = ExceptionUtils.getExceptionAudit(joinPoint, ex);
            exceptionAudit.setApplicationName(applicationName);
            exceptionAudit.setPromptId(promptId);
            auditService.sendExceptionAudit(exceptionAudit);

            if (ex instanceof TransientAiException ||
                        ex instanceof NonTransientAiException ||
                    ex instanceof OpenAiApiClientErrorException) {
                throw new AiException(ex.getMessage(), ex);
            } else {
                throw new RuntimeException(ex.getMessage(), ex);
            }
        }
    }

    /**
     * Extracts the promptId from the method arguments.
     * Currently, assumes the first String argument is the promptId.
     *
     * @param args the method arguments to search through
     * @return the extracted promptId, or null if not found
     */
    private String extractPromptId(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof String) { // Check if argument is a String
                return (String) arg; // Assuming `promptId` is of type String
            }
        }
        return null; // Return null if not found
    }

}

