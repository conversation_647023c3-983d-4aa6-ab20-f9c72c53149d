package com.enttribe.commons.ai.audit.tool;


public class ToolAuditHelper {

    private static final ThreadLocal<ToolAuditHelper.ToolAuditContext> toolAuditContextThreadLocal = new ThreadLocal<>();

    public static void set(ToolAuditHelper.ToolAuditContext toolAuditContext) {
        toolAuditContextThreadLocal.set(toolAuditContext);
    }

    public static ToolAuditHelper.ToolAuditContext get() {
        return toolAuditContextThreadLocal.get();
    }

    public static void clear() {
        toolAuditContextThreadLocal.remove(); // always clear to avoid memory leaks
    }

    public static class ToolAuditContext {

        private String auditId;
        private String agentName;
        private String promptId;
        private String promptName;
        private String applicationName;
        private String model;
        private String provider;
        private String status;


        public String getApplicationName() {
            return applicationName;
        }

        public String getModel() {
            return model;
        }

        public String getProvider() {
            return provider;
        }

        public String getStatus() {
            return status;
        }

        public String getAuditId() {
            return auditId;
        }

        public String getAgentName() {
            return agentName;
        }

        public String getPromptId() {
            return promptId;
        }

        public String getPromptName() {
            return promptName;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private final ToolAuditContext toolAuditContext;

            private Builder() {
                toolAuditContext = new ToolAuditHelper.ToolAuditContext();
            }

            public ToolAuditContext.Builder applicationName(String applicationName) {
                toolAuditContext.applicationName = applicationName;
                return this;
            }

            public ToolAuditContext.Builder model(String model) {
                toolAuditContext.model = model;
                return this;
            }

            public ToolAuditContext.Builder provider(String provider) {
                toolAuditContext.provider = provider;
                return this;
            }

            public ToolAuditContext.Builder status(String status) {
                toolAuditContext.status = status;
                return this;
            }
            public ToolAuditContext.Builder auditId(String auditId) {
                toolAuditContext.auditId = auditId;
                return this;
            }

            public ToolAuditContext.Builder agentName(String agentName) {
                toolAuditContext.agentName = agentName;
                return this;
            }

            public ToolAuditContext.Builder promptId(String promptId) {
                toolAuditContext.promptId = promptId;
                return this;
            }

            public ToolAuditContext.Builder promptName(String promptName) {
                toolAuditContext.promptName = promptName;
                return this;
            }

            public ToolAuditContext build() {
                return toolAuditContext;
            }
        }
    }

}