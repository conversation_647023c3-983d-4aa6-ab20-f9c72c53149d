package com.enttribe.commons.ai.audit;

import com.enttribe.commons.ai.config.RestTemplateSingleton;
import com.enttribe.commons.ai.model.audit.ExceptionAudit;
import com.enttribe.commons.ai.model.audit.PromptAudit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@ConditionalOnExpression(
        "#{environment.getProperty('exception.audit.enable', 'false') == 'true' and " +
                "environment.getProperty('prompt.audit.enable', 'false') == 'false'}"
)
public class ExceptionAuditServiceImpl implements AuditService {

    private static final Logger log = LoggerFactory.getLogger(ExceptionAuditServiceImpl.class);

    @Value("${prompt.service.url:http://prompt-analyzer-service.ansible.svc.cluster.local/prompt-analyzer/rest}")
    private String promptServiceUrl;

    public ExceptionAuditServiceImpl() {
        log.debug("prompt audit is disabled and exception audit is enabled for ai-commons");
    }

    @Async
    @Override
    public void sendExceptionAudit(ExceptionAudit exceptionAudit) {

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
        // Create HTTP headers (you can add more headers like Authorization if needed)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

        // Wrap the ExceptionAudit in an HttpEntity, which also carries the headers
        HttpEntity<ExceptionAudit> entity = new HttpEntity<>(exceptionAudit, headers);

        try {
            // Make the POST request to the audit endpoint
            ResponseEntity<String> response = restTemplate.exchange(
                    promptServiceUrl + "/audit/exception/save",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            // Check if the request was successful and return the response body
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("exception audit sent successfully");
            } else {
                log.warn("failed to send exception audit");
            }

        } catch (Exception e) {
            log.error("Error while sending error audit : {}", e.getMessage(), e);
        }
    }


    @Override
    public void sendPromptAudit(PromptAudit promptAudit) {
        //Do nothing
    }
}
