package com.enttribe.commons.ai.audit;

import com.enttribe.commons.ai.model.audit.ExceptionAudit;
import com.enttribe.commons.ai.model.audit.PromptAudit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnExpression(
        "#{environment.getProperty('exception.audit.enable', 'false') == 'false' and " +
                "environment.getProperty('prompt.audit.enable', 'false') == 'false'}"
)
public class NoOpAuditService implements AuditService {

    private static final Logger log = LoggerFactory.getLogger(NoOpAuditService.class);

    public NoOpAuditService() {
        log.debug("audit is disabled for ai-commons");
    }

    @Override
    public void sendExceptionAudit(ExceptionAudit exceptionAudit) {
        //Do nothing
    }

    @Override
    public void sendPromptAudit(PromptAudit promptAudit) {
        //Do nothing
    }
}
