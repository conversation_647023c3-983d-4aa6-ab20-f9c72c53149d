package com.enttribe.commons.ai.audit;

import com.enttribe.commons.ai.model.audit.ExceptionAudit;
import com.enttribe.commons.ai.model.audit.PromptAudit;

/**
 * Service interface for handling audit-related operations in the AI Commons system.
 * This interface provides methods for sending different types of audit events,
 * including exception audits and prompt audits.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuditService {

    /**
     * Sends an exception audit event to the audit system.
     * This method is used to track and record exceptions that occur during system operation.
     *
     * @param exceptionAudit the exception audit event to be sent
     * @throws IllegalArgumentException if the exceptionAudit parameter is null
     */
    void sendExceptionAudit(ExceptionAudit exceptionAudit);

    /**
     * Sends a prompt audit event to the audit system.
     * This method is used to track and record prompt-related activities in the system.
     *
     * @param promptAudit the prompt audit event to be sent
     * @throws IllegalArgumentException if the promptAudit parameter is null
     */
    void sendPromptAudit(PromptAudit promptAudit);

}
