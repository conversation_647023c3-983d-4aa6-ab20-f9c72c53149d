package com.enttribe.commons.ai.audit.tool;

import com.enttribe.commons.ai.model.audit.ToolAudit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(prefix = "tool.audit", name = "enable", havingValue = "false", matchIfMissing = true)
public class NoOpToolAuditService implements ToolAuditService {

    private static final Logger log = LoggerFactory.getLogger(NoOpToolAuditService.class);

    public NoOpToolAuditService() {

    }

    @Override
    public void sendToolAudit(ToolAudit toolAudit) {
        //No implementation
    }

}
