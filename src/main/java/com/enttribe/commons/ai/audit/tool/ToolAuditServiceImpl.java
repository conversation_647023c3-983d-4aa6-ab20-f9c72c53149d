package com.enttribe.commons.ai.audit.tool;

import com.enttribe.commons.ai.config.RestTemplateSingleton;
import com.enttribe.commons.ai.model.audit.ToolAudit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
@Primary
@ConditionalOnProperty(prefix = "tool.audit", name = "enable", havingValue = "true")
public class ToolAuditServiceImpl implements ToolAuditService {

    private static final Logger log = LoggerFactory.getLogger(ToolAuditServiceImpl.class);

    @Value("${prompt.service.url:http://prompt-analyzer-service.ansible.svc.cluster.local/prompt-analyzer/rest}")
    private String promptServiceUrl;

    public ToolAuditServiceImpl() {
        log.info("tool audit is enabled in commons ai sdk");
    }

    @Override
    @Async
    public void sendToolAudit(ToolAudit toolAudit) {

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
        // Create HTTP headers (you can add more headers like Authorization if needed)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

        // Wrap the ExceptionAudit in an HttpEntity, which also carries the headers
        HttpEntity<ToolAudit> entity = new HttpEntity<>(toolAudit, headers);

        try {
            restTemplate.exchange(
                    promptServiceUrl + "/audit/toolAudit/save",
                    HttpMethod.POST,
                    entity,
                    Map.class
            );


        } catch (Exception e) {
            log.error("Error while sending tool audit : {}", e.getMessage(), e);
        }
    }
}
