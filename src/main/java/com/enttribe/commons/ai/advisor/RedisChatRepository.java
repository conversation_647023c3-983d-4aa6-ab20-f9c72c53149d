package com.enttribe.commons.ai.advisor;


import com.enttribe.commons.ai.deserializer.MessageDeserializer;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class RedisChatRepository implements ChatMemoryRepository {

    private static final Logger logger = LoggerFactory.getLogger(RedisChatRepository.class);
    private static final String KEY_PREFIX = "chat:conversation:";
    private static final int EXPIRATION_TIME_MINUTES = 15;

    private final RedisTemplate<String, Object> redisTemplate;
    private static final ObjectMapper objectMapper = getObjectMapper();

    /**
     * Constructs a new RedisChatMemory instance.
     *
     * @param redisTemplate the Redis template to use for storage operations
     */
    public RedisChatRepository(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        logger.info("Initializing RedisChatRepository with Redis template");
    }

    /**
     * Configures and returns an ObjectMapper instance with custom deserializers.
     *
     * @return configured ObjectMapper instance
     */
    private static ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Message.class, new MessageDeserializer());
        objectMapper.registerModule(module);
        return objectMapper;
    }

    /**
     * Generates a Redis key for a given conversation ID.
     *
     * @param conversationId the ID of the conversation
     * @return the Redis key for the conversation
     */
    private String getRedisKey(String conversationId) {
        return KEY_PREFIX + conversationId;
    }


    @Override
    public List<String> findConversationIds() {
        return List.of();
    }

    @Override
    public List<Message> findByConversationId(String conversationId) {
        logger.debug("Retrieving messages for conversation {}", conversationId);

        String redisKey = getRedisKey(conversationId);
        String serializedMessages = (String) redisTemplate.opsForValue().get(redisKey);

        if (serializedMessages == null) {
            logger.debug("No messages found for conversation {}", conversationId);
            return List.of();
        }

        try {
            List<Message> allMessages = objectMapper.readValue(
                    serializedMessages,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Message.class)
            );

            logger.debug("Retrieved {} messages from conversationId {}", allMessages.size(), conversationId);
            return allMessages;

        } catch (JsonProcessingException e) {
            logger.error("Failed to deserialize messages for conversation {}: {}", conversationId, e.getMessage());
            throw new RuntimeException("Failed to deserialize messages", e);
        }
    }

    @Override
    public void saveAll(String conversationId, List<Message> messages) {
        logger.debug("Adding {} messages to conversation {}", messages.size(), conversationId);

        try {
            String redisKey = getRedisKey(conversationId);
            String serializedMessages = objectMapper.writeValueAsString(messages);
            redisTemplate.opsForValue().set(redisKey, serializedMessages, EXPIRATION_TIME_MINUTES, TimeUnit.MINUTES);
            logger.debug("Successfully stored {} messages for conversation {}", messages.size(), conversationId);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize messages for conversation {}: {}", conversationId, e.getMessage());
            throw new RuntimeException("Failed to serialize messages", e);
        }
    }

    @Override
    public void deleteByConversationId(String conversationId) {
        logger.debug("Clearing messages for conversation {}", conversationId);
        String redisKey = getRedisKey(conversationId);
        Boolean deleted = redisTemplate.delete(redisKey);
        if (deleted) {
            logger.debug("Successfully cleared conversation {}", conversationId);
        } else {
            logger.debug("No messages found to clear for conversation {}", conversationId);
        }
    }
}
