package com.enttribe.commons.ai.advisor;

import com.enttribe.commons.ai.config.RestTemplateSingleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.AdvisorChain;
import org.springframework.ai.chat.client.advisor.api.BaseAdvisor;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * An advisor implementation that provides LLM (Language Learning Model) input validation and security checks.
 * This advisor integrates with an external LLM Guard service to scan and validate prompts before they are processed.
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class LlmGuardAdvisor implements BaseAdvisor {

    @Value("${commons.ai.sdk.llm.guard.url:http://10.1.0.202:10001/scan/prompt}")
    private String llmGuardUrl;

    private static final Logger log = LoggerFactory.getLogger(LlmGuardAdvisor.class);


    private boolean isSafe(ChatClientRequest advisedRequest) {
        Boolean guard = (Boolean) advisedRequest.context().get("guard");
        String userMessage = (String) advisedRequest.context().get("userMessage");
        advisedRequest.context().remove("guard");
        advisedRequest.context().remove("userMessage");

        boolean isSafe = true;
        if (guard != null && guard && userMessage != null) {
            log.debug("Starting LLM Guard validation for user message");
            Map<String, Object> result = executeLlmGuard(userMessage);
            Boolean isValid = (Boolean) result.get("is_valid");
            if (isValid != null && !isValid) {
                isSafe = false;
                Map<String, Double> scanners = (Map<String, Double>) result.get("scanners");
                List<String> nonZeroKeys = getNonZeroKeys(scanners);
                log.warn("LLM Guard validation failed. Violations detected: {}", nonZeroKeys);
            }
        } else {
            log.debug("LLM Guard validation passed successfully");
        }
        return isSafe;
    }

    /**
     * Returns the name of this advisor.
     *
     * @return the advisor name
     */
    @Override
    public String getName() {
        return "LlmGuardAdvisor";
    }

    @Override
    public ChatClientRequest before(ChatClientRequest chatClientRequest, AdvisorChain advisorChain) {
        boolean safe = isSafe(chatClientRequest);
        if (!safe) {
            throw new RuntimeException("GUARDRAIL_EXCEPTION");
        }
        return chatClientRequest;
    }

    @Override
    public ChatClientResponse after(ChatClientResponse chatClientResponse, AdvisorChain advisorChain) {
        return chatClientResponse;
    }

    /**
     * Returns the order of this advisor in the chain.
     *
     * @return the order value (0 indicates highest priority)
     */
    @Override
    public int getOrder() {
        return 0;
    }

    /**
     * Extracts keys from a map where the corresponding values are non-zero.
     *
     * @param inputMap the map to process
     * @return a list of keys whose values are non-zero
     */
    public List<String> getNonZeroKeys(Map<String, Double> inputMap) {
        if (inputMap == null || inputMap.isEmpty()) {
            log.debug("Input map is null or empty, returning empty list");
            return Collections.emptyList();
        }

        return inputMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue() != 0.0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * Executes the LLM Guard validation by sending the input to the guard service.
     *
     * @param input the prompt text to validate
     * @return a map containing the validation results
     */
    private Map<String, Object> executeLlmGuard(String input) {
        log.debug("Executing LLM Guard validation for input");
        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

//        String llmGuardUrl = "http://10.1.0.202:10001/scan/prompt";
        log.debug("Sending request to LLM Guard service at: {}", llmGuardUrl);

        HttpEntity<Map<String, String>> entity = new HttpEntity<>(Map.of("prompt", input), headers);

        try {
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    llmGuardUrl,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<Map<String, Object>>() {
                    }
            );
            log.info("LLM Guard service response received successfully");
            log.debug("response from llm guard : {}", response.getBody());
            return response.getBody();
        } catch (Exception e) {
            log.error("Failed to execute LLM Guard validation. Error: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * Creates a context map for LLM Guard configuration based on the guard flag and messages.
     *
     * @param llmGuard boolean flag indicating whether LLM Guard should be enabled
     * @param messages list of messages to process
     * @return a map containing guard configuration and user message
     */
    public static Map<String, Object> getGuardContext(Boolean llmGuard, List<Message> messages) {
        llmGuard = llmGuard == null || llmGuard;
        Message userMessage = messages.stream()
                .filter(message -> message.getMessageType().equals(MessageType.USER))
                .findFirst().orElse(null);

        if (userMessage == null) {
            log.debug("No user message found in messages list");
            return Map.of("guard", llmGuard);
        }
        log.debug("Created guard context with enabled={}", llmGuard);
        return Map.of("guard", llmGuard, "userMessage", userMessage.getText());
    }

}
