package com.enttribe.commons.ai.advisor;

import com.enttribe.commons.ai.audit.AuditService;
import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.audit.AuditContext;
import com.enttribe.commons.ai.model.audit.PromptAudit;
import com.enttribe.commons.ai.util.AuditUtils;
import com.enttribe.commons.ai.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.metadata.Usage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class AuditAdvisor implements CallAdvisor, StreamAdvisor {

    private static final Logger log = LoggerFactory.getLogger(AuditAdvisor.class);
    @Value("${commons.ai.sdk.app.name}")
    private String applicationName;

    @Value("${prompt.audit.enable:false}")
    private boolean promptAuditEnabled;

    private final AuditService auditService;

    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAILED = "failed";

    public AuditAdvisor(AuditService auditService) {
        this.auditService = auditService;
    }

    @Override
    public ChatClientResponse adviseCall(ChatClientRequest chatClientRequest, CallAdvisorChain callAdvisorChain) {
        List<Message> messages = new ArrayList<>(chatClientRequest.prompt().getInstructions());

        OpenAiChatOptions openAiChatOptions = (OpenAiChatOptions) chatClientRequest.prompt().getOptions();
        AuditContext auditContext = (AuditContext) openAiChatOptions.getToolContext().get("auditContext");
        if (auditContext == null) {
            log.warn("audit context is found null in tool context : {}", openAiChatOptions.getToolContext());
            return callAdvisorChain.nextCall(chatClientRequest);
        }
        chatClientRequest.context().remove("auditContext");
        String conversationAuditId = (String) openAiChatOptions.getToolContext().get("conversationAuditId");
        if (conversationAuditId != null) {
            log.info("setting conversationAuditId in aroundCall");
            auditContext.setAuditId(conversationAuditId);
        }
        List<ToolCallback> toolCallbacks = openAiChatOptions.getToolCallbacks();
        String toolCallDefinitions = JsonUtils.convertToJSON(toolCallbacks);
        ChatClientResponse advisedResponse = null;
        try {
            Instant startTime = Instant.now();
            Date startTimeDate = new Date();
            advisedResponse = callAdvisorChain.nextCall(chatClientRequest);
            Instant endTime = Instant.now();
            Date endTimeDate = new Date();
            long timeTakenMillis = Duration.between(startTime, endTime).toMillis();
            ChatResponse chatResponse = advisedResponse.chatResponse();
            doSuccessPromptAudit(auditContext, timeTakenMillis, chatResponse, messages, toolCallDefinitions, startTimeDate, endTimeDate);
            return advisedResponse;
        } catch (Exception e) {
            doFailedPromptAudit(auditContext, messages, e.getMessage(), toolCallDefinitions);
            throw e;
        }
    }

    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest chatClientRequest, StreamAdvisorChain streamAdvisorChain) {
        // Process messages similar to aroundCall method
        List<Message> messages = new ArrayList<>(chatClientRequest.prompt().getInstructions());

        // Get audit context from request
        OpenAiChatOptions openAiChatOptions = (OpenAiChatOptions) chatClientRequest.prompt().getOptions();
        AuditContext auditContext = (AuditContext) openAiChatOptions.getToolContext().get("auditContext");
        if (auditContext == null) {
            log.warn("audit context is found null in stream tool context : {}", openAiChatOptions.getToolContext());
            return streamAdvisorChain.nextStream(chatClientRequest);
        }
        chatClientRequest.context().remove("auditContext");
        String conversationAuditId = (String) openAiChatOptions.getToolContext().get("conversationAuditId");
        if (conversationAuditId != null) {
            log.info("setting conversationAuditId in streamCall");
            auditContext.setAuditId(conversationAuditId);
        }

        List<ToolCallback> toolCallbacks = openAiChatOptions.getToolCallbacks();
        String toolCallDefinitions = JsonUtils.convertToJSON(toolCallbacks);
        try {
            // Create audit record immediately for the request without waiting for responses
            doStreamingPromptAudit(auditContext, messages, toolCallDefinitions);
           log.debug("audit record created for streaming");
            // Get the stream of responses and pass them through
            return streamAdvisorChain.nextStream(chatClientRequest);
        } catch (Exception e) {
            log.error("error occurred in aroundStream for audit : {}", e.getMessage(), e);
            doStreamingFailedPromptAudit(auditContext, messages, e.getMessage(), toolCallDefinitions);
            return Flux.error(e);
        }
    }

    @Override
    public String getName() {
        return "AuditAdvisor";
    }

    @Override
    public int getOrder() {
        return 999;
    }

    private void doSuccessPromptAudit(AuditContext auditContext, long timeTakenMillis,
                                      ChatResponse chatResponse, List<Message> messages, String toolCallDefinition,
                                      Date startTime, Date endTime) {
        log.info("prompt audit id : {} startTime : {} endTime : {}", auditContext.getAuditId(), startTime, endTime);

        if (promptAuditEnabled) {
            //Audit related code
            PromptModel promptModel = auditContext.getPromptModel();
            String auditId = auditContext.getAuditId();
            Usage usage = chatResponse.getMetadata().getUsage();

            PromptAudit promptAudit = PromptAudit.builder()
                    .applicationName(applicationName)
                    .agentName(promptModel.getAgentName())
                    .chatOptions(AuditUtils.getChatOptionsMap(promptModel))
                    .auditId(AuditUtils.generateAuditId(auditId, promptModel.getApplicationName()))
                    .promptId(promptModel.getPromptId())
                    .model(promptModel.getModel())
                    .responseText(chatResponse.getResult().getOutput().getText())
                    .totalToken(usage.getTotalTokens())
                    .promptToken(usage.getPromptTokens())
                    .generationTokens(usage.getCompletionTokens())
                    .responseTime(timeTakenMillis)
                    .promptName(promptModel.getPromptName())
                    .creationTime(new Date())
                    .startTime(startTime)
                    .endTime(endTime)
                    .provider(promptModel.getProvider())
                    .requestText(JsonUtils.convertToJSON(messages))
                    .toolCallDefinitions(toolCallDefinition)
                    .status(STATUS_SUCCESS)
                    .build();

            auditService.sendPromptAudit(promptAudit);
        }
    }

    private void doFailedPromptAudit(AuditContext auditContext,
                                     List<Message> messages, String errorMessage, String toolCallDefinition) {
        if (promptAuditEnabled) {
            //Audit related code
            PromptModel promptModel = auditContext.getPromptModel();
            String auditId = auditContext.getAuditId();

            PromptAudit promptAudit = PromptAudit.builder()
                    .applicationName(applicationName)
                    .chatOptions(AuditUtils.getChatOptionsMap(promptModel))
                    .auditId(AuditUtils.generateAuditId(auditId, promptModel.getApplicationName()))
                    .promptId(promptModel.getPromptId())
                    .model(promptModel.getModel())
                    .promptName(promptModel.getPromptName())
                    .creationTime(new Date())
                    .provider(promptModel.getProvider())
                    .requestText(JsonUtils.convertToJSON(messages))
                    .httpStatus(extractStatusCode(errorMessage))
                    .status(STATUS_FAILED)
                    .errorMessage(errorMessage)
                    .toolCallDefinitions(toolCallDefinition)
                    .build();

            auditService.sendPromptAudit(promptAudit);
        }
    }

    private static Integer extractStatusCode(String errorMessage) {
        try {
            Pattern pattern = Pattern.compile("^(\\d{3})");
            Matcher matcher = pattern.matcher(errorMessage);

            if (matcher.find()) {
                return Integer.parseInt(matcher.group(1)); // Extracts the first 3-digit number
            }
        } catch (Exception e) {
            log.error("error in extracting status code : {} text : {}", e.getMessage(), errorMessage);
        }

        return null; // Return null if no status code is found
    }


    private void doStreamingPromptAudit(AuditContext auditContext, List<Message> messages, String toolCallDefinition) {
        if (promptAuditEnabled) {
            //Audit related code
            PromptModel promptModel = auditContext.getPromptModel();
            String auditId = auditContext.getAuditId();

            PromptAudit promptAudit = PromptAudit.builder()
                    .applicationName(applicationName)
                    .agentName(promptModel.getAgentName())
                    .chatOptions(AuditUtils.getChatOptionsMap(promptModel))
                    .auditId(AuditUtils.generateAuditId(auditId, promptModel.getApplicationName()))
                    .promptId(promptModel.getPromptId())
                    .model(promptModel.getModel())
                    // No response text for streaming initial audit
                    .promptName(promptModel.getPromptName())
                    .creationTime(new Date())
                    .provider(promptModel.getProvider())
                    .requestText(JsonUtils.convertToJSON(messages))
                    .toolCallDefinitions(toolCallDefinition)
                    .status(STATUS_SUCCESS)
                    .build();

            auditService.sendPromptAudit(promptAudit);
        }
    }

    private void doStreamingFailedPromptAudit(AuditContext auditContext,
                                              List<Message> messages, String errorMessage, String toolCallDefinition) {
        if (promptAuditEnabled) {
            //Audit related code
            PromptModel promptModel = auditContext.getPromptModel();
            String auditId = auditContext.getAuditId();

            PromptAudit promptAudit = PromptAudit.builder()
                    .applicationName(applicationName)
                    .chatOptions(AuditUtils.getChatOptionsMap(promptModel))
                    .auditId(AuditUtils.generateAuditId(auditId, promptModel.getApplicationName()))
                    .promptId(promptModel.getPromptId())
                    .model(promptModel.getModel())
                    .promptName(promptModel.getPromptName())
                    .creationTime(new Date())
                    .provider(promptModel.getProvider())
                    .requestText(JsonUtils.convertToJSON(messages))
                    .httpStatus(extractStatusCode(errorMessage))
                    .status(STATUS_FAILED)
                    .toolCallDefinitions(toolCallDefinition)
                    .errorMessage(errorMessage)
                    .build();

            auditService.sendPromptAudit(promptAudit);
        }
    }

}
