package com.enttribe.commons.ai.advisor;

import com.enttribe.commons.ai.deserializer.MessageDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.data.redis.core.RedisTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.module.SimpleModule;

/**
 * Redis-based implementation of the ChatMemory interface for storing and managing conversation history.
 * This implementation provides persistent storage of chat messages using Redis as the backend store.
 * Messages are serialized to JSON before storage and deserialized when retrieved.
 * Each conversation is stored with a unique key and expires after 15 minutes of inactivity.
 *
 * <AUTHOR>
 * @see ChatMemory
 * @since 1.0.0
 */
@Deprecated
public class RedisChatMemory implements ChatMemory {

    private static final Logger logger = LoggerFactory.getLogger(RedisChatMemory.class);
    private static final String KEY_PREFIX = "chat:conversation:";
    private static final int EXPIRATION_TIME_MINUTES = 15;

    private final RedisTemplate<String, Object> redisTemplate;
    private static final ObjectMapper objectMapper = getObjectMapper();

    /**
     * Constructs a new RedisChatMemory instance.
     *
     * @param redisTemplate the Redis template to use for storage operations
     */
    public RedisChatMemory(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        logger.debug("Initializing RedisChatMemory with Redis template");
    }

    /**
     * Configures and returns an ObjectMapper instance with custom deserializers.
     *
     * @return configured ObjectMapper instance
     */
    private static ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Message.class, new MessageDeserializer());
        objectMapper.registerModule(module);
        return objectMapper;
    }

    /**
     * Generates a Redis key for a given conversation ID.
     *
     * @param conversationId the ID of the conversation
     * @return the Redis key for the conversation
     */
    private String getRedisKey(String conversationId) {
        return KEY_PREFIX + conversationId;
    }

    @Override
    public void add(String conversationId, Message message) {
        ChatMemory.super.add(conversationId, message);
    }

    /**
     * Adds new messages to an existing conversation. If the conversation doesn't exist,
     * it creates a new one. Messages are stored in Redis with a 15-minute expiration time.
     *
     * @param conversationId the ID of the conversation to add messages to
     * @param messages       the list of messages to add
     * @throws RuntimeException if message serialization fails
     */
    @Override
    public void add(String conversationId, List<Message> messages) {
        logger.debug("Adding {} messages to conversation {}", messages.size(), conversationId);

        List<Message> existingMessages = get(conversationId, Integer.MAX_VALUE);
        List<Message> updatedMessages = new ArrayList<>(existingMessages);
        updatedMessages.addAll(messages);

        try {
            String redisKey = getRedisKey(conversationId);
            String serializedMessages = objectMapper.writeValueAsString(updatedMessages);
            redisTemplate.opsForValue().set(redisKey, serializedMessages, EXPIRATION_TIME_MINUTES, TimeUnit.MINUTES);
            logger.debug("Successfully stored {} messages for conversation {}", updatedMessages.size(), conversationId);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize messages for conversation {}: {}", conversationId, e.getMessage());
            throw new RuntimeException("Failed to serialize messages", e);
        }
    }

    @Override
    public List<Message> get(String conversationId) {
        logger.debug("Retrieving messages from conversation {}", conversationId);

        String redisKey = getRedisKey(conversationId);
        String serializedMessages = (String) redisTemplate.opsForValue().get(redisKey);

        if (serializedMessages == null) {
            logger.debug("No messages found for conversation {}", conversationId);
            return List.of();
        }

        try {
            return objectMapper.readValue(
                    serializedMessages,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Message.class)
            );
        } catch (JsonProcessingException e) {
            logger.error("Failed to deserialize messages for conversation {} : {}", conversationId, e.getMessage());
            throw new RuntimeException("Failed to deserialize messages", e);
        }
    }

    /**
     * Retrieves the last N messages from a conversation.
     *
     * @param conversationId the ID of the conversation to retrieve messages from
     * @param lastN          the maximum number of most recent messages to retrieve
     * @return a list containing at most lastN most recent messages
     * @throws RuntimeException if message deserialization fails
     */
    public List<Message> get(String conversationId, int lastN) {
        logger.debug("Retrieving last {} messages from conversation {}", lastN, conversationId);

        List<Message> allMessages = get(conversationId);
        List<Message> result = allMessages.stream()
                .skip(Math.max(0, allMessages.size() - lastN))
                .toList();

        logger.debug("Retrieved {} messages from conversation {}", result.size(), conversationId);
        return result;
    }

    /**
     * Clears all messages for a given conversation.
     *
     * @param conversationId the ID of the conversation to clear
     */
    @Override
    public void clear(String conversationId) {
        logger.debug("Clearing messages for conversation {}", conversationId);
        String redisKey = getRedisKey(conversationId);
        Boolean deleted = redisTemplate.delete(redisKey);
        if (deleted) {
            logger.debug("Successfully cleared conversation {}", conversationId);
        } else {
            logger.info("No messages found to clear for conversation {}", conversationId);
        }
    }
}
