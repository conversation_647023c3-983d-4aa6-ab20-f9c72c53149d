package com.enttribe.commons.ai.scheduler;

import com.enttribe.commons.ai.chat.AiChatModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@EnableScheduling
public class SdkScheduler {

    private static final Logger log = LoggerFactory.getLogger(SdkScheduler.class);
    private final AiChatModel aiChatModel;

    /**
     * Initializes the SDK Scheduler with required dependencies.
     *
     * @param aiChatModel The AiChatModel interface
     */
    public SdkScheduler(AiChatModel aiChatModel) {
        this.aiChatModel = aiChatModel;
    }

    /**
     * Scheduled task that refreshes the SDK maps as done on application start-up. It refreshed chat model map, embedding model map, prompt models and vector store map.
     * This method runs periodically based on the configured interval (default: 900000ms = 15 minutes)
     * with an initial delay of 60 seconds after application startup.
     */
    @Scheduled(fixedDelayString = "${commons.ai.sdk.prompt.refresh.interval:900000}", initialDelay = 60_000)
    public void refreshSdk() {
        aiChatModel.refresh();
    }

}
