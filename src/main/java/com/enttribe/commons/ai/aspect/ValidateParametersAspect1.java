package com.enttribe.commons.ai.aspect;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.RecordComponent;
import java.util.Iterator;

@Aspect
@Component
public class ValidateParametersAspect1 {

    private static final Logger logger = LoggerFactory.getLogger(ValidateParametersAspect.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Around("execution(* *(.., @com.enttribe.conversationalai.annotation.ValidateParameters (*), ..))")
    public Object validateParameters(ProceedingJoinPoint joinPoint) throws Throwable {
        logger.info("Aspect executed");
        Object[] args = joinPoint.getArgs();
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod(); // Get the method signature
        Class<?> returnType = method.getReturnType(); // Get the return type dynamically

        // Iterate through method arguments
        for (Object arg : args) {
            if (arg instanceof Record) {
                Record request = (Record) arg;

                // Iterate through the record components
                RecordComponent[] components = request.getClass().getRecordComponents();
                for (RecordComponent component : components) {
                    Object value = component.getAccessor().invoke(request);

                    // Check if parameter is annotated with @Body
                    Body bodyAnnotation = component.getAnnotation(Body.class);
                    if (bodyAnnotation != null) {
                        if (value == null || !(value instanceof String jsonString)) {
                            return createResponse(returnType, "Invalid or null JSON payload for parameter: " + component.getName());
                        }

                        try {
                            logger.info("jsonString is : {}", jsonString);
                            JsonNode jsonPayload = objectMapper.readTree(jsonString);
                            String schemaJson = bodyAnnotation.value();
                            JsonNode schema = objectMapper.readTree(schemaJson);
                            JsonNode requiredFields = schema.get("required");

                            if (requiredFields != null && requiredFields.isArray()) {
                                Iterator<JsonNode> iterator = requiredFields.elements();
                                while (iterator.hasNext()) {
                                    String fieldName = iterator.next().asText();
                                    // Check for field existence and value in JSON payload
                                    logger.info("fieldName is : {}", fieldName+jsonPayload.get(fieldName).toString());
                                    if (!jsonPayload.has(fieldName) || jsonPayload.get(fieldName).isNull() || jsonPayload.get(fieldName).toString().isEmpty()) {
                                        return createResponse(returnType, "Missing or empty required field in JSON: " + fieldName);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            return createResponse(returnType, "Failed to parse JSON payload for parameter: " + component.getName());
                        }
                    } else {
                        // Check non-@Body parameters
                        if (value == null || value.toString().isEmpty()) {
                            logger.info("Missing value for parameter: " + component.getName());
                            return createResponse(returnType, "Missing value for parameter: " + component.getName());
                        }
                    }
                }
            }
        }

        // Proceed if validation passes
        return joinPoint.proceed();
    }

    /**
     * Dynamically creates an instance of the response type and sets the message.
     */
    private Object createResponse(Class<?> returnType, String message) {
        try {
            // Try to find a constructor that accepts a single String argument (message)
            Constructor<?> constructor = returnType.getConstructor(String.class);
            return constructor.newInstance(message);
        } catch (Exception e) {
            e.printStackTrace();
            // Return a default object if constructor is not found
            return null;
        }
    }
}