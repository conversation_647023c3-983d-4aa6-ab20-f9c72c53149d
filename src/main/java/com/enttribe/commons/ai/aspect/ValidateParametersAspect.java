package com.enttribe.commons.ai.aspect;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.RecordComponent;
import java.util.Iterator;

@Component
public class ValidateParametersAspect {

    private static final Logger logger = LoggerFactory.getLogger(ValidateParametersAspect.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    public String validateParameters(Object request) throws Throwable {
        logger.info("Aspect executed");

        // Iterate through the record components
        RecordComponent[] components = request.getClass().getRecordComponents();
        for (RecordComponent component : components) {
            Object value;
            try {
                Method accessor = component.getAccessor();
                accessor.setAccessible(true); // Override access checks
                value = accessor.invoke(request);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access component accessor: " + component.getName(), e);
            }

            // Check if parameter is annotated with @Body
            Body bodyAnnotation = component.getAnnotation(Body.class);
            if (bodyAnnotation != null && bodyAnnotation.value() != null) {
                String jsonString = bodyAnnotation.value();

                try {
                    logger.debug("function parameters are : {}", jsonString);
                    JsonNode jsonPayload = objectMapper.readTree(jsonString);
                    String schemaJson = bodyAnnotation.value();
                    JsonNode schema = objectMapper.readTree(schemaJson);
                    JsonNode requiredFields = schema.get("required");

                    if (requiredFields != null && requiredFields.isArray()) {
                        Iterator<JsonNode> iterator = requiredFields.elements();
                        while (iterator.hasNext()) {
                            String fieldName = iterator.next().asText();
                            // Check for field existence and value in JSON payload
                            logger.debug("fieldName is : {} : {}", fieldName, jsonPayload.get("properties").get(fieldName));
                            boolean hasRequiredField =
                                    !(jsonPayload.get("properties").get(fieldName) == null) &&
                                    !jsonPayload.get("properties").get(fieldName).isEmpty();
                            if (!hasRequiredField) {
                                return "Missing or empty required field in JSON: " + fieldName;
                            }
                        }
                    }
                } catch (Exception e) {
                    return "Failed to parse JSON payload for parameter: " + component.getName();
                }
            } else {
                // Check non-@Body parameters
                if (value == null || value.toString().isEmpty()) {
                    logger.info("Missing value for parameter: {}", component.getName());
                    return "Missing value for parameter: " + component.getName();
                }
            }
        }
        // Proceed if validation passes
        return null;
    }


}
